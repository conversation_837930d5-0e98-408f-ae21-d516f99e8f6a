{"@@locale": "en", "appName": "StickyQR", "homePageTitle": "Homepage", "loginSignIn": "Sign in", "loginEmail": "Email", "loginEmailAddress": "Email address", "loginPassword": "Password", "loginForgotPassword": "Forgot Password?", "loginSignUp": "Sign Up", "loginHaveAccount": "You don't have an account?", "emailInvalid": "<PERSON><PERSON> is invalid", "emailRequired": "Email is required", "phoneInvalid": "Phone number invalid", "phoneRequired": "Phone number is required", "phoneIncorrect": "Phone number is incorrect", "passwordRequired": "Password is required", "registerSignUp": "Sign up", "registerSignIn": "Sign in", "registerEmailAddress": "Email address", "registerPassword": "Password", "registerHaveAccount": "Already have an account?", "forgotPassword": "Forgot Password", "forgotPasswordDescription": "Please enter the email address linked with your account", "send": "Send", "resendCode": "Resend", "verify": "Verify", "tryAgain": "Try Again", "codeRequired": "Verification code is required", "confirm": "Confirm", "resetPassword": "Reset Password", "backToSignIn": "Back to Sign In", "passwordChanged": "Password Changed", "passwordChangedDescription": "Your password has been changed successfully", "enterCode": "Enter code", "enterCodeDescription": "Enter your verification code toreset your password", "incorrectVerificationCode": "Incorrect verification code", "enterPasswordDescription": "Create a password with at least 8 characters", "payAtFrontDesk": "Pay at Front Desk", "payOnline": "Pay online", "cancelBooking": "Cancel booking", "apply": "Apply", "enterYourCode": "Enter your code", "thanksYou": "Thank You!", "later": "Later", "notFound": "Not found", "search": "Search", "select": "Select", "selectCountry": "Select country", "firstName": "First Name", "lastName": "Last Name", "gender": "Gender", "dateOfBirth": "Date of birth", "email": "Email", "phone": "Phone", "password": "Password", "male": "Male", "female": "Female", "others": "Others", "add": "Add", "edit": "Edit", "profile": "Profile", "editProfile": "Edit Profile", "emailAddress": "Email address", "updateEmail": "Update email", "updateEmailMessage": "We'll send you an email to confirm your email address", "addAMobileNumber": "Add a mobile number", "addAMobileNumberMessage": "We'll text you a code to verify your phone number", "country": "Country", "mobileNumber": "Mobile number", "imageFromGalleryTitle": "Image from gallery", "takeAPhotoTitle": "Take a Photo", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "notUpdatedYet": "Not updated yet", "yearOld": "Year old", "cancel": "Cancel", "save": "Save", "ok": "Ok", "updatePassword": "Update password", "currentPassword": "Current password", "newPassword": "New password", "confirmPassword": "Confirm password", "confirmPasswordRequired": "Confirm password is required", "confirmPasswordInvalid": "Confirm password does not match", "newPasswordInvalid": "The new password cannot match your current password", "currentPasswordRequired": "Current password is required", "newPasswordRequired": "New password is required", "register": "Register", "notifications": "Notifications", "youHaveNoNotification": "No Notifications Yet", "notifiEmptyMessage": "You have no notifications right now.", "markAsRead": "<PERSON> as read", "yes": "Yes", "no": "No", "welcome": "Welcome", "newStatus": "New", "confirmedStatus": "Confirmed", "checkedInStatus": "Checked In", "checkedOutStatus": "Checked Out", "cancelledStatus": "Cancelled", "noShowStatus": "No Show", "markAsUnread": "<PERSON> as unread", "confirmLogoutMsg": "Are you sure you want to log out?", "confirmDeleteMsg": "Are you sure you want to delete?", "delete": "Delete", "signInOPT": "Sign In OTP", "signInPhoneOrUserName": "Phone or username", "otp": "OTP", "forgotPhoneOrEmail": "Phone or Email", "back": "Back", "next": "Next", "emailPhoneRequired": "Email or Phone is required", "emailPhoneInvalid": "Email or Phone is invalid", "phoneNumber": "Phone number", "verificationCode": "Verification code", "signInSignUp": "Sign in or sign up to StickyQR", "youHave": "You have not set up password yet", "passwordIsUsed": "Password is used to secure and verify your account.", "setUpNow": "Set up now", "switchAccount": "Switch account", "businessName": "Business name", "businessNameRequired": "Business name is required", "codeIsIncorrect": "Code is incorrect", "logOut": "Sign out", "lockScreen": "Lock screen", "codesScanned": "# Codes Scanned", "today": "Today", "thisMonth": "This Month", "sticky": "<PERSON>y", "enterPoints": "Enter points", "enterPointsRequired": "Points is required", "points": "Points", "qty": "Quantity", "show": "Show QR", "createAndShow": "Create and show qr code.", "print": "Print", "createAndPrint": "Create and print qr code.", "printerSetting": "Printer setting", "customer": "Customer", "go": "Go", "customers": "Customers", "settings": "Settings", "enterPhone": "Enter phone", "qrCodeManagement": "QR code management", "stickyNomal": "StickyQR", "scanQR": "<PERSON>an the QR Code to receive points.", "notQRCode": "Invalid QR Code!", "pleaseEnterPasscode": "Please enter passcode", "successfully": "Successfully", "enterPhoneNumber": "Enter your phone number", "invalidPhoneOrPass": "Invalid Phone number or Password!", "empty": "Empty", "printEmty": "Printer not found", "printerName": "Printer name", "printerNameRequired": "Printer name is required", "scan": "<PERSON><PERSON>", "ipSetting": "IP setting", "noPrinter": "No printer found - Try again or IP setting", "ipAddress": "IP address", "port": "Port", "addPrinter": "Add Printer", "editPrinter": "Edit Printer", "printTest": "Print test", "unableToConnect": "Unable to connect", "ipAddressRequired": "IP address is required", "wifiEthernetPrinters": "Wifi/ Ethernet printers", "labelsTemplate": "Labels template", "defaultText": "<PERSON><PERSON><PERSON>", "instanceStickyQR": "Instance StickyQR", "customTemplate": "Label template", "printers": "Printers", "printer": "Printer", "size": "Size", "qr": "QR", "align": "Align", "left": " Left ", "right": "Right", "width": "<PERSON><PERSON><PERSON>", "content": "Content", "line": "Line", "printerIsRequired": "Printer is required", "claimed": "This code can be claimed only once", "printersSettings": "Printers", "noPrintersAreAvailable": "No printers are available", "pressToSetUpAPrinter": "Press (+) to set up a printer", "enterCharacters": "Enter text", "ipAddressIncorrect": "IP address is incorrect", "connectToPrinter": "Printing is not available. Please check your settings or the printer itself.", "scanning": "Scanning...", "setUp": "Set up now", "msgSnackBar": "No printer or print template configured", "bottomContent": "Bottom content", "point": "Point", "redeem": "Redeem", "total": "Total", "error": "Error!", "close": "Close", "guest": "Guest", "photo": "Photo", "nameReward": "Name reward", "nameRewardRequired": "Name is required", "editReward": "Edit reward", "deleteReward": "Delete reward", "createReward": "Create reward", "rewards": "Rewards", "redeemRewards": "<PERSON><PERSON><PERSON>", "redemptionComplete": "Redemption complete", "redemptionSuccessful": "Redemption successful!", "followingRewards": "for the following rewards?", "rewardsToRedeem": "There are currently no rewards to redeem", "currentlyNoRewards": "There are currently no rewards", "opps": "Opps!", "upload": "Upload", "addReward": "Add reward", "warning": "Warning", "fileIsLarge": "File is too large", "labelTemplate": "Label Template", "arrangeRewards": "Arrange rewards", "printersAvailable": "No printers are available", "selectPrinter": "Select printer", "dpi": "Select printer dpi", "dpiRequired": "Printer DPI is required", "sendCode": "Send code", "textMessage": "Text Message", "voiceCall": "Voice Call", "receiveCodeTitle": "Didn't receive a code?", "verificationCodeVia": "Receive verification code via", "enterSendCodeTitle": "Enter the code sent to your device", "resendTextTitle": "Please enter the code texted to your phone{andemail}. To receive a phone call instead,", "@resendTextTitle": {"description": "and email", "placeholders": {"andemail": {"type": "String"}}}, "andEmail": " and email", "resendCallTitle": "Please enter the code you hear on the call. To receive it by text instead,", "resendTextCode": "Text again", "resendCallCode": "Call again", "tapHere": "tap here.", "successForgor": "Password has been updated", "codeScanEmptyTitle": "There are no codes scanned", "passWordRequiedCharacter": "Password must have at least 6 characters.", "passWordNewRequiedCharacter": "New password must have at least 6 characters.", "staffEdit": "Your changes were saved", "staffCreate": "User has been created", "staffEditTitle": "Edit user", "staffCreateTitle": "Create user", "staffs": "Users", "name": "Name", "nameRequired": "Name is required", "staffsEmptyTitle": "There are no user", "businessProfile": "Business Profile", "timezone": "Timezone", "currency": "<PERSON><PERSON><PERSON><PERSON>", "countryCode": "Country Code", "street": "Street", "city": "City", "state": "State", "optional": "optional", "zipcode": "Zipcode", "saved": "Saved", "rewardHasBeenCreated": "Reward has been created", "rewardHasBeenUpdated": "Reward has been updated", "rewardHasBeenDeleted": "<PERSON><PERSON> has been deleted", "thePointsHasBeenAdded": "The {points} {have} been given", "@thePointsHasBeenAdded": {"description": "points", "placeholders": {"points": {"type": "String"}, "have": {"type": "String"}}}, "preview": "Preview", "setPassword": "Set user password! (optional)", "activities": "Activities", "done": "Done", "toDay": "Today", "thisWeek": "This week", "thisMonthActivities": "This month", "custom": "Custom", "all": "All", "scanned": "Scanned", "rewardsRedeemed": "Redeemed", "pointsEarned": "Given", "codesScannedTitle": "Scanned", "active": "Active", "inactive": "Inactive", "announcements": "Announcements", "announcementEmptyTitle": "There are no announcements", "announcementEdit": "Your changes were saved", "announcementCreate": "Announcement has been created", "editAnnouncement": "Edit announcement", "deleteAnnouncement": "Announcement has been deleted", "createAnnouncement": "Create announcement", "description": "Description", "descriptionIsRequired": "Description is required", "images": "Images (optional)", "announcement": "Announcement", "selectIcons": "Select an icon", "limitImages": "Number of images should not be more than {limit}", "@limitImages": {"description": "limit images", "placeholders": {"limit": {"type": "String"}}}, "activitiesEmpty": "There's no data to show right now!", "voidType": "Voided", "voidTypeTitle": "Void", "addCustomer": "Would you like to add this customer?", "customerName": "Customer name (Optional)", "lessThanOne": "Points must not be less than 1", "confirmVoidMsg": "Are you sure you want to void activity?", "createdBy": "Created by", "voidedBy": "Voided by", "date": "Date", "by": "by", "rewardsTitle": "Rewards", "copied": "Phone number copied.", "enterPointsLabel": "[Points]", "arrangeAnnouncemnts": "Arrange announcements", "language": "Language", "account": "Account", "signOut": "Sign out", "business": "Business", "english": "English", "vietnamese": "Vietnamese", "yourName": "Your name", "yourNameRequired": "Your name is required", "changePassword": "Change password", "changePasswordALike": "New password cannot be same as your current password", "continueTitle": "Continue", "getCodeBy": "Get code by", "phoneCall": "Phone call", "useAnotherMethod": "Use another method", "receiveCode": "Didn't receive code?", "enterEmail": "Enter your email address", "codeSendEmailTitle": "Enter the code we sent over email to", "codeCallPhoneTitle": "Enter the code that was called to", "codeSMSPhoneTitle": "Enter the code we sent over SMS to", "checkNetwork": "Check your network connection and try again", "choose": "<PERSON><PERSON>", "locationAccess": "Please allow location access or turn on your location of device", "map": "Map", "change": "Change", "enterPassword": "Enter your password", "checkConfirmPasswod": "Passwords do not match", "confirmNewPassword": "Confirm new password", "confirmNewPasswordRequired": "Confirm new password is required", "confirmPassWordNewRequiedCharacter": "Confirm new password must have at least 6 characters.", "enterStreet": "Enter street", "referralProgram": "Referral program", "pointsForReferrerUser": "Points for referrer user", "pointsForReferrerUserRequired": "Points for referrer user is required", "pointsForReferrerUserValid": "Points must not be less than 0", "pointsForReferrerUserSubtitle": "When you refer someone to us and they complete a specific action, you'll receive these reward points.", "pointsForReferredUser": "Points for referred user", "pointsForReferredUserRequired": "Points for referred user is required", "pointsForReferredUserSubtitle": "When you are referred by someone and complete a specific action, you'll receive these reward points.", "requiredPointsForCompletion": "Required points for completion", "requiredPointsForCompletionRequired": "Required points for completion is required", "requiredPointsForCompletionSubtitle": "To complete the referral process, you need to accumulate this amount of points. Keep earning points by participating in activities within the store and achieving the set targets.", "referralProgramTitle": "Program to accumulate points for members when inviting friends to accumulate points at the store", "referralProgramEnabled": "Enabled the \"Referral Program\" feature to advertise to attract more members to accumulate points and redeem rewards at the store.", "remove": "Remove", "noteRequiredPoint": "Note: If you set \"Required Points = 0\", the referrer and the referee will get the reward immediately.", "referrerType": "<PERSON><PERSON><PERSON>", "addIcon": "Add icon", "thumbnail": "<PERSON><PERSON><PERSON><PERSON>", "addPreviewAnnouncementIcon": "Preview announcement", "image": "Image", "imageIsRequired": "Image is required", "useThumbnail": "Use an image as a thumbnail.", "defaultDescription": "Business name with a short description.", "uploadImage": "Upload Image", "editImage": "Edit Image", "contentWillDisplayedUsers": "The content will be displayed to the customers.", "indicateOrHideAnnouncement": "Indicate or hide announcement for customers.", "donForgetToTap": "Don't forget to tap the", "buttonSeeHow": "button to see how it will be displayed to customers.", "signageEditItemsMessages": "Select and arrange the rewards you want to display on the template", "template": "Template", "selectAll": "Select all", "printPreview": "Preview & Print", "dontForgetTapThe": "Don't forget to tap the", "buttonToSeeHowTheTemp": "button to see how the template will display", "signage": "Signage", "couterCards": "Counter Cards", "poweredBy": "Powered by", "share": "Share", "getStartedCounterCard": "Counter card", "counterCardTitleContent": "Counter card {number}", "@counterCardTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "contentTooltip": "The content will be displayed to the users.", "title": "Title", "titleRequired": "Title is required", "subtitleRequired": "Description is required", "qrCodeContent": "QR code content", "qrCodeContentTooltip": "Enter the link to change the QR code to match your ads content.", "url": "URL", "urlIsRequired": "URL is required", "footer": "Footer", "footerTooltip": "Indicate or Hide footer.", "forgetTo": "Don't forget to tap the", "previewAndPrint": "\"Preview & Print\"", "templateWillDisplay": "button to see how the template will display", "previewPrint": "Preview & Print", "previewCounterCard1": "Preview counter card #1", "units": "Units", "height": "height", "urlIsIncorrect": "URL is incorrect", "getStarted": "Get started", "downloadThe": "Download the", "app": "app", "givenPoints": "Given points", "scanToJoinOur": "<PERSON><PERSON> to join our free rewards program", "closeBusiness": "Close Business", "closeBusinessMsg1": "If you close your business account, all customer's information will be permanently deleted.", "closeBusinessMsg2": "You can still open a new business account with the same phone number.", "definitelyCloseMyBusiness": "Definitely close my business", "pleaseEnterTheCode": "Please enter the code texted to your phone", "toReceiveAPhoneCallInstead": "To receive a phone call instead,", "msgAlertCall": "Please enter the code you hear on the call. To receive it by text instead,", "textAgain": "Text again", "callAgain": "Call again", "staffDeleted": "User has been deleted", "deleteStaff": "Delete user", "cutLabel": "Cut label", "customize": "CUSTOMIZE", "update": "Update", "updateProfileTitle": "Please update your business information. This will help customers access it more easily.", "printTestNote": "Adjust the printer when the print test is incorrect.", "adjust": "Adjust", "printTestAllCase": "Print test all case", "notePrinterChangeSizeText1": "Note: When you change the ", "labelSize": "label size", "notePrinterChangeSizeText2": ", the printout may not be accurate. You need to adjust the print size to match the label size correctly (Tap the \"Adjust\" button below).", "againExitApp": "Press again to exit", "unsavedChanges": "Unsaved changes", "unsavedChangesSubtitle": "Changes you made may not be saved.", "saveChanges": "Save Changes", "leaveWithoutSaving": "Leave without saving", "management": "Manage...", "detail": "Detail", "newCustomer": "New", "returning": "Returning", "pending": "Pending", "planExceeded": "Action required!", "notiUpgradeMsgScanQR": "A customer just scanned the QR code. However, your Plan has exceeded the allowed limit.", "notiUpgradeMsgRefferal": "A customer has just joined the Referral Program. However, your Plan has exceeded the allowed limit.", "pendingNote": "Plan Business exceeded the allowed limit.", "type": "Type", "sourceQRScanned": "QR Scanned", "sourceReferral": "Referral", "quantity": "Quantity: ", "printerQueue": "Printer <PERSON>", "printing": "Printing", "queue": "Queue", "basic": "Basic", "gold": "Gold", "platinum": "Platinum", "join": "Join", "notiUpgradeMsgJoin": "A customer just joined your business. However, your Plan has exceeded the allowed limit.", "guestDelete": "Deleted", "statistics": "Statistics", "unlimited": "Unlimited", "participant": "Participant", "campaigns": "Campaigns", "credit": "Credits", "codesScannedUsage": "Codes scanned", "redeemRewardsUsage": "Rewards redeemed", "newCustomersUsage": "New customers", "returningCustomersUsage": "Returning customers", "week": "Week", "month": "Month", "curentMonth": "Current month", "pendingApprovals": "Pending Approvals{total}", "@pendingApprovals": {"description": "total", "placeholders": {"total": {"type": "String"}}}, "approve": "Approve", "reject": "Reject", "pendingApprovalsAlertMsg": "There are {quantity} customers or transactions pending approval. Please review and approve as needed.", "@pendingApprovalsAlertMsg": {"description": "quantity", "placeholders": {"quantity": {"type": "String"}}}, "pendingApprovalsEmpty": "There's no data to show right now!", "skip": "<PERSON><PERSON>", "addAnnouncement": "Add Announcement", "history": "History", "customersEmpty": "There's no data to show right now!", "customersNotFound": "There's no customer match search!", "loading": "Loading", "boTitle": "Want more Information?", "boSubtitle": "Use StickyQR's Back Office to unlock more features for business owners.", "accessNow": "Access now", "titleNotNetwork": "No internet connection. Retrying...", "titleNetwork": "Connected.", "somethingWentWrong": "Something went wrong.", "currentcustomers": "Customers", "rewardsProgram": "Rewards program", "forFree": "For free", "quickStartGuide": "Quick start guide", "setUpBusinessProfile": "Set up Business profile", "setUpRewards": "Set up Rewards", "setUpPrinterAndLabel": "Set up Printer & Label", "referralProgramQuickSetUp": "Referral Program", "complete": "Complete", "kindlyCreateAtLeastOneRewardForCustomerToRedeem": "Kindly create at least one reward for customers to redeem their points.", "youCanReferToSomeSampleRewardsSuggestedByAITecnology": "You can refer to some sample rewards suggested by AI technology.", "aIGenerate": "AI Generate", "generate": "Generate", "newRewards": "New Reward", "suggetsSetupRewardsDescription": "Suggest 10 reward items for dishes like Coffee, Sandwich,... with point ranges from 10-100 points.", "submit": "Submit", "inputYourReward": "Input your reward", "exampleReward": "Ex: <PERSON><PERSON>", "joinRewards": "Join rewards", "programForFree": "Program for free", "billingPaymentMethod": "Billing", "currentCredit": "Current credit", "addOns": "Add ons", "manager": "Manage", "invoices": "Invoices", "invoicesSubtitle": "Manage your payment receipts", "upcoming": "Upcoming", "viewInvoice": "View Invoice", "paymentMethods": "Payment methods", "updateCard": "Update card", "businessAddress": "Business address", "updateAddress": "Update address", "additionalInformation": "Additional information", "addInformation": "Add information", "addInformationSubtitle": "VAT ID and other information to show on your invoices.", "paid": "Paid", "expirationMonth": "Expiration month", "expirationYear": "Expiration year", "expiration": "Expiration", "cvc": "CVC", "cardNumber": "Card number", "cardNumberRequired": "Card number is required", "nameOnCard": "Name on card", "nameOnCardRequired": "Name on card is required", "vatGSTID": "VAT/GST ID", "informationToShowOnYourInvoices": "Information to show on your invoices", "touchHereToAccessMenu": "Touch here to access Menu", "touchHereToSetUpProfile": "Touch here to set up your profile", "touchHereToSelectSettings": "Touch here to select settings", "touchHereToSetUpReferralProgram": "Touch here to set up the referral program", "touchHereToInstalllPrinterAndEditLabel": "Touch here to install printer and edit label template.", "qtyPrintQueue": "qty", "plan": "Plan", "free": "Free", "monthly": "Monthly", "yearly": "Yearly", "mostPopular": "Most popular", "currentPlan": "Current plan", "contact": "Contact us", "freeSubtitle": "Free forever. No credit card", "priceMonthlySubtitle": "per month", "priceYearlySubtitle": "per month billed annually", "titleSelectedPlan": "Are you sure you want to upgrade to this {plan} package?", "@titleSelectedPlan": {"description": "plan", "placeholders": {"plan": {"type": "String"}}}, "firstWelcome": "Welcome!", "firstSolution": "Solution", "firstToolsIntegration": "Tools Integration", "firstDesStep1": "Your solution for boosting customer loyalty and driving repeat business through effective loyalty programs.", "firstDesStep2": "Reward customers with points for purchases and easily redeem earned points for discounts or free products.", "firstDisplaysPrintingOptionForQRCodes": "Displays & Printing Option for QR codes", "firstCampaignMessage": "Campaign message", "firstSignage": "Signage", "setAsdeDault": "Set as default", "expires": "Exp", "expiresTitle": "Expires", "addAPayment": "Add a payment method", "viewAll": "View All", "invoiceEmptyTitle": "There are no invoices", "creditsPurchased": "Credits Purchased", "planUpgradedGoldMonthly": "Monthly Subscription for Gold", "planUpgradedGoldYearly": "Yearly Subscription for Gold", "planUpgradedPlatinumMonthly": "Monthly Subscription for Platinum", "planUpgradedPlatinumYearly": "Yearly Subscription for Platinum", "removeCard": "Remove card", "confirmRemoveCardMsg": "Are you sure you want to remove card?", "monthRequired": "Month is required", "yearRequired": "Year is required", "streetRequired": "Street is required", "cityRequired": "City is required", "billingAddress": "Billing Address", "addPaymentMethod": "Add payment method", "cvcRequired": "CVC is required", "expirationRequired": "Expiration is required", "expirationInvalid": "Expiration is invalid", "thereAreNoActiveAnnouncements": "There are no active announcements", "thereAreNoInactiveAnnouncements": "There are no inactive announcements", "thereAreNoActiveRewards": "There are no active rewards", "thereAreNoInactiveRewards": "There are no inactive rewards", "pleaseCreateAtLeastOneRewardToDisplayOnTheTemplate": "Please create at least one reward to display on the template", "address": "Address", "addressRequired": "Address is required", "addCardSuccess": "Your card has been successfully added", "deleteCardSuccess": "Your card has been successfully deleted", "paymentEmpty": "There are no Payment methods in your account yet.", "easyAndFree": "Easy & Free", "theEasiestWayToExciteCustomers": "The easiest way to excite customers and bring them back again & again with loyalty points - for free.", "anytimeAnyWhere": "Anytime Anywhere", "sendCustomersMessagesAndExclusiveOffers": "Send customers messages and exclusive vouchers once they've departed. They're eager to stay connected and to find more reasons to come back!", "getYourBusinessUpAndRunningIn15Minutes": "Get your business up & running in 15 minutes!", "letGo": "Let's go", "noData": "There is no data", "confirmRejectMsg": "Are you sure you want to reject?", "confirmApproveMsg": "Are you sure you want to approve?", "updateApp": "Update App?", "whatIsNews": "What's news?", "updateAppTitle": "A new version of {appName} is available! Version {storeVersion} is now available-you have {currentVersion}", "@updateAppTitle": {"description": "and email", "placeholders": {"appName": {"type": "String"}, "storeVersion": {"type": "String"}, "currentVersion": {"type": "String"}}}, "updateNow": "Update now", "adjustCoordinates": "Adjust Coordinates", "addCoordinates": "Add Coordinates", "countryRequired": "Country is required", "selectCurrency": "Select currency", "selectTimeZone": "Select timezone", "approveTotalMsg": "The number of customers who accept ({noAllow}) cannot exceed the maximum number of customers ({noMax})", "@approveTotalMsg": {"description": "", "placeholders": {"noAllow": {"type": "int"}, "noMax": {"type": "int"}}}, "approveAll": "Approve all", "rejectAll": "Reject all", "confirmRejectAllMsg": "Are you sure you want to reject all?", "confirmApproveAllMsg": "Are you sure you want to approve all?", "enterAddress": "Enter address", "security": "Security", "confirmPasswordDoesNotMatch": "Confirm password does not match", "setPasswordBusiness": "Set password", "deducted": "Deducted", "deductedStatus": "Deducted", "deduct": "Deduct", "reason": "Reason (optional)", "reasonLabel": "Reason", "thePointsHasBeenDeduct": "The {points} {have} been deducted", "@thePointsHasBeenDeduct": {"description": "points", "placeholders": {"points": {"type": "String"}, "have": {"type": "String"}}}, "deductPoints": "Deduct points", "has": "has", "have": "have", "offerPeriod": "Voucher period", "offerDetail": "Voucher detail", "offerRedeem": "Vouchers", "offerRedeemSuccess": "The voucher redemption was successful.", "offerRedeemError": "The voucher redemption was unsuccessful.", "offerType": "Voucher redeemed", "offerError": "Cannot apply voucher", "offers": "Vouchers", "printError": "During the printing process, some labels were not successfully printed. Do you want to continue printing?", "draftOfferStatus": "Draft", "activeOfferStatus": "Scheduled", "runningOfferStatus": "Running", "offersEmpty": " There are no vouchers", "startDate": "Start date", "endDate": "End date", "status": "Status", "maxQuantity": "Max quantity", "maxPerUser": "Max per user", "private": "Private", "public": "Public", "performance": "Performance", "remaining": "Remaining", "claimedOffer": "Claimed", "offer": "Voucher", "redeemedOffer": "Redeemed", "givePoints": "Give points", "from": "From", "to": "to", "redeemOffer": "Redeem Voucher?", "offersToRedeem": "There are currently no vouchers to redeem", "tags": "Tags", "internalIdentifiersThatYouCanUseToCategorizeYourCustomers": "Internal identifiers that you can use to categorize your customers.", "clearAll": "Clear all", "enterTagName": "Enter tag name", "create": "Create", "theNameMustNotExceed128Characters": "The name must not exceed 128 characters.", "offerCode": "Voucher code", "delivered": "Delivered", "available": "Available", "giveOffer": "Give voucher", "giveTiltle": "Give", "claimOfferSuccessful": "Claim successful!", "scheduled": "Scheduled", "offersToClaim": "There are currently no vouchers", "paused": "Paused", "completed": "Completed", "dateClaimTitle": "When voucher can be claimed", "dateRedeemTitle": "When voucher can be redeemed", "tagsTitle": "Tags", "enterTitle": "Enter title", "setDefault": "Set default label", "deleteLabel": "Delete label", "titleLabelTemplate": "Title label template", "validityPeriod": "Receive time:", "labelsEmpty": "There's no label!", "labelOfferTitleText": "SCAN THE QR TO CLAIM", "labelOfferText": "Text", "labelOfferTitle": "[Title]", "labelOfferCode": "[Voucher code]", "labelOfferDate": "[Voucher Dates]", "labelPoints": "[Points]", "labelscanQR": "SCAN QR", "labelsToGet": "TO GET", "addLabel": "Add Label", "printingStarting": "Printing is starting", "titleDupLabel": "Buy 1 get 1", "codeDupLabel": "BUY1GET1", "yourPhoneNumber": "Your phone number", "loginCustomerTitle": "Please enter your password", "loginCustomerSubTitle": "You already have an account. Do you want to use this phone number to register your business?", "stickyQRApp": "STICKYQR App", "downloadTheSignage": "Download The", "joinRewardProgram": "Join <PERSON>", "buyCredit": "Manage Credits", "textCredit": "Credits", "buy": "Buy", "currentlyBalance": "Credit Balance", "offerCreateTitle": "Create voucher", "offerEditTitle": "Edit voucher", "overview": "Overview", "offerName": "Voucher name", "offerNameSubTitle": "Give your voucher an internal name to help organize and locate it easily within your account.", "offerTitle": "Voucher title", "offerContent": "Voucher content", "offerContentSubtitle": "Set the text of the message your customers will receive.", "addOfferContent": "Add voucher content", "offerHeader": "Header (Optional)", "offerHeaderSubtitle": "Use an image as a header", "offerStartDate": "Start date", "offerEndDate": "End date (Optional)", "datePicker": "Date picker", "timePicker": "Time picker", "maximumQuantity": "Maximum quantity", "maximumQuantitySub": "Maximum of vouchers for all users", "maximumPerUser": "Maximum per user", "maximumPerUserSub": "Maximum number of vouchers a user can receive", "addTag": "Add tag", "saveAndExit": "Save & Exit", "saveAndStart": "Save & Start", "customerPayment": "Customer payment", "enterContent": "Enter content", "dates": "Dates", "noExpiration": "No expiration", "performaceOffer": "Performance voucher", "showQR": "Show QR", "printLabel": "Print label", "titleOfferCode": "Scan the QR Code to receive voucher.", "item": "<PERSON><PERSON>", "currentlyCredit": "Currently credit", "payment": "Payment", "billAmount": "Enter amount", "addNote": "Add note", "deleteNote": "Delete note", "sendPaymentLink": "Send payment link", "scanQrCodeToPay": "Scan QR code to pay", "note": "Note", "pleaseEnterTheAmount": "Please enter the amount", "confirmPhoneNumber": "Confirm phone number", "pleaseEnterAValidAmount": "Please enter a valid amount.", "imagesOffer": "Images", "permissionLocalNetworkTitle": "The application needs local network permissions, please provide permissions.", "help": "Help", "printerHelpStep1": "The device needs to connect to the same local area network as the printer.", "printerHelpStep2": "Provide local area network access to the device.", "printerHelpStep3": "You can find and input the IP address of the printer manually.", "printerHelpStep31": "Simultaneously press and hold the \"Feed\" and \"Cancel\" buttons → Release when the light flashes → The IP address will appear in the \"IP ADDRESS\" field.", "printerHelpStep32": "Xprinter XP420B: Turn off the power → Press and hold the \"Feed\" button → Turn on the power → Release when the light changes to purple → The IP address will appear in the \"DEFAULT GATEWAY\" field.", "printerHelpStep4": "Print test, if the label isn't properly aligned, press the Adjust button to realign.", "setting": "Setting", "amount": "Amount", "tip": "Tip", "paymentMethod": "Payment method", "card": "Card", "msgTooltipCard": "Paid by Card", "msgTooltipGG": "Paid via Google Pay", "msgTooltipApple": "Paid via Apple Pay", "msgTooltipVisaCard": "Paid by Visa Card", "msgTooltipMasterCard": "Paid by Master Card", "msgTooltipAmexCard": "Paid by Amex Card", "pointClaims": "Point claims", "pointClaimsSubTitle": "No customer has made a claim yet!", "totalPointClaims": "{data} customer{s}", "@totalPointClaims": {"description": "data point claim", "placeholders": {"data": {"type": "int"}, "s": {"type": "String"}}}, "givePointClaimTitle": "Give", "signageGetStarted": "Get started", "signagePointClaims": "Point Claims", "pointClaimsCounterCard": "Point Claims", "pointClaimsTitleContent": "Point claims {number}", "@pointClaimsTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "scanToClaimYourPoints": "Scan to\nclaim\nyour points", "rejectAllPointClaimTitle": "Reject all", "rollIn": "Roll in", "confirmRejectAllPointLaimsMsg": "Are you sure you want to reject all?", "pointClaimNotif": "{name} just made a request for some points!", "@pointClaimNotif": {"description": "", "placeholders": {"name": {"type": "String"}}}, "pointClaimGiveConfirm": "Please confirm {points} point{s} to be given.", "@pointClaimGiveConfirm": {"description": "", "placeholders": {"points": {"type": "String"}, "s": {"type": "String"}}}, "features": "Features", "featuresPointClaims": "Point Claims", "featureNew": "New", "enabledPointClaims": "Enabled", "disabledPointClaims": "Disabled", "allowUsersToScanQrCodesAtYourBusiness": "Allows users to initiate requests for points by scanning static QR codes.", "programToAccumulatePointsForCustomers": "Gives points for referring friends to your business.", "businessPhone": "Business phone", "businessPhoneRequired": "Business phone is required", "referrals": "Referrals", "engagement": "Engagement", "soundSettings": "Sound", "enableSoundPointClaims": "Enable sound notification for new point claims", "beep3Times": "<PERSON><PERSON> 3 times", "continuousBeeping": "Continuous beeping until acknowledged", "notificationSound": "Notification sound", "tink": "Tink", "pop": "Pop", "beep": "Link", "offerClaimed": "Voucher claimed", "showInAppOnly": "Show in app only (StickyQR)", "appOnly": "App-only", "explore": "Explore", "homeCallButton": "Call <PERSON>ton", "homeClaimPoints": "<PERSON>laim points", "homeReject": "Reject", "homeGive": "Give", "homeEnterPoints": "Enter points", "homeMarkDoneCompleted": "Mark all completed", "homeShowAllRewards": "Show all Rewards", "homeShowLess": "Show less", "homeCloseRequestAfter": "Closing request after", "homeUndo": "Undo", "homeCloseOut": "Close out", "homeRedeemPoints": "Redeem points", "homeThereAreNoRequests": "There are no requests", "homeCallBtnPleaseSelectARewardToRedeem": "Please select a reward to redeem", "homeCallBtnCloseOutInactiveTable": "Close out inactive tables", "homeCallBtnThereAreNoRequestPendingAtThisTime": "There are no requests pending at this time", "callButton": "Call <PERSON>ton", "callButtonTitle": "Call button", "tables": "Tables", "service": "Services", "callButtonSubTitle": "Integrate Call Button for swift, polished customer assistance, ensuring you never miss a request from remote customers.", "request": "Request", "action": "Function", "externalLink": "External Link", "link": "Link", "scanQRTitle": "Scan QR", "addNewSection": "Add Section", "sectionName": "Section name", "renameSectionTable": "<PERSON><PERSON>", "printAllTablesQR": "Print QR Code for all Tables", "addANewTable": "Add table", "tablesService": "Table services", "setTableService": "Set all service for{table}table{s}", "@setTableService": {"description": "", "placeholders": {"table": {"type": "String"}, "s": {"type": "String"}}}, "renameService": "<PERSON><PERSON>", "addANewService": "Add service", "editANewService": "Edit service", "serviceName": "Service name", "cbSectionNameRequired": "Section name is required", "newSection": "New Section", "qrCode": "QR code", "redeemPoints": "Redeem points", "survey": "Survey", "qrContent": "QR Content", "enterServiceName": "Enter service name", "suggest": "Suggest", "prefix": "Prefix", "numberOfTables": "Number of tables", "none": "None", "serviceTitle": "{total} Service{s}", "@serviceTitle": {"description": "", "placeholders": {"total": {"type": "String"}, "s": {"type": "String"}}}, "cbcounterCardTitleContent": "Template {number}", "@cbcounterCardTitleContent": {"description": "", "placeholders": {"number": {"type": "String"}}}, "subTitle": "Subtitle", "callButtonSignageTitle": "CALL BUTTON", "callButtonSignageSubTitle": "(why wait?)", "callButtonSignageDescription": "Call your server. Get the bill. Check on your order. Need more water, sauces, utensils, to-go containers or something else, why wait?", "fontSize": "Font size", "spacing": "Spacing", "anchorTextBox": "Anchor text box", "tableSubtitle": "Manane tables and sections, asign services to tables, print QR codes.", "serviceSubtitle": "Manage services where customers and the store interact.", "deleteSection": "Delete Section", "editTable": "Edit Table & Services", "tableService": "Table services", "iconRequired": "Icon is required", "linkRequired": "Link is required", "qrCodeRequired": "QR code is required", "beverage": "Beverage", "food": "Food", "fruit": "Fruit", "other": "Other", "seasonings": "Seasonings", "sweets": "Sweets", "utensils": "utensils", "vegetables": "Vegetables", "customizeTitle": "Customize", "soundSettingSub": "Play sound for notification.", "thankYouSettings": "Thank You page", "thankYouSettingsSub": "Show message after closing out table.", "deleteSectionTitle": "Do you want to delete \"{section}\"?", "@deleteSectionTitle": {"description": "section", "placeholders": {"section": {"type": "String"}}}, "noteDeleteTable": "Note: If you delete this section, the entire table will be deleted.", "noteDeleteService": "Note: If you delete this section, the entire service will be deleted.", "numberOfTablesRequired": "Number of tables is required", "tablesServiceRequired": "Table services is required", "cbSelectAll": "Select All", "renameSectionTitle": "Rename Section", "table": "Table", "hintAddSection": "e.g Floor 1", "linkInvalid": "Link is invalid", "cbEnableSound": "Enable sound notification for new request", "enterYourMessage": "Enter your message...", "callToActionButtonTitle": "Primary button", "buttonTitle": "Button title", "thankYouTitle": "Thank you!", "thanksMsgTitle": "We sincerely thank you for your business today and hope to be able to serve you again soon.", "goToHomePage": "Go to home page", "buttonTitleRequired": "Button title is required", "beta": "Beta", "serviceSectionName": "Group name", "serviceSectionNameRequired": "Group name is required", "deleteServiceSection": "Delete group", "tableName": "Table name", "assignedServices": "Assigned Services ({total})", "@assignedServices": {"description": "total", "placeholders": {"total": {"type": "String"}}}, "autoServices": "Automatically add new services", "autoServicesSub": "The new services will be automatically added to this table if selected.", "untitleServiceGroup": "(Untitle Service Group)", "untitleSectionTable": "(Untitle Section Table)", "newGroup": "New Group", "serviceGroupNameHint": "Service group name (optional)", "manageTableServices": "Manage Table Services", "common": "Common", "utensilsSeasonings": "Utensils & Seasonings", "foodBeverages": "Food & Beverages", "fruitsVegetables": "Fruits & Vegetables", "chooseUserAction": "Choose user action:", "requestSubtitle": "Users send a request, like calling for a bill,…", "actionSubtitle": "User performs actions such as Point Claims,...", "externalLinkSubtitle": "User clicks to access an external link.", "qrCodeService": "QR Code", "qrCodeSubtitle": "Displays a QR Code for users to scan", "assignThisService": "Assign this service to the tables?", "assignedTables": "Select Tables", "templateCallButton": "Choose template", "businessLogoTitle": "Display Business Logo", "businessNameTitle": "Display Business Name", "actionSelection": "Action Selection", "actionSelectionSubtitle": "Select the type of action the user can perform", "chooseAnAction": "Choose an Action", "functionName": "Function Name", "actionSelectionRequired": "Action is required", "learnMore": "Learn more", "pointClaimsCB": "Point Claims", "redeemPointsCB": "Redeem Points", "tablesEmpty": "There are no tables yet!", "staffNote": "Note", "staffTags": "Tags", "staffAddTag": "Add Tag", "staffNoteContent1": "Your staff member will set their own password through the StickyQR Manager app.", "staffNoteContent2": "If the staff member has used the StickyQR system before, they can enter the password they previously used.", "rolesPermissionsTitle": "Roles & Permissions", "rolePermissionSubtitle": "Define roles and assign permissions", "roleTitle": "Role", "staffInternalIndentifiersThatYouCanUseToCategorizeYourStaff": "Internal identifiers that you can use to categorize your user.", "staffInternalIndentifiersThatYouCanUseToCategorizeYourCustomer": "Internal identifiers that you can use to categorize your customer.", "staffRole": "Role", "customerAddTags": "Add Tags", "owner": "Owner", "roleIsRequired": "Role is required", "general": "General", "businessSettings": "Business Settings", "multipleHome": "Multiple Home", "stickyPoints": "Sticky Points", "stickyVouchers": "Sticky Vouchers", "stickyCallButton": "<PERSON><PERSON>", "read": "Read", "checkAll": "Check All", "settingsPoints": "Points Settings", "settingsVouchers": "Vouchers Settings", "settingsCallButton": "Manage Call Button feature permissions", "allow": "Allow", "roleName": "Enter role name", "roleNameRequired": "Role name is required", "warningDeleteRole": "This role is currently assigned to someone else. To delete this role, assign a different role to the user who currently has it", "transferDelete": "Transfer & Delete", "noRole": "You don't have access", "serviceTokens": "Service Tokens", "customersPayment": "Customers Payment", "paymentGateways": "Payment Gateways", "voidTransactions": "Void Transactions", "managerTablesServices": "Manager Tables & Services", "manageCustomersRequests": "Manage Customers Requests", "rolesPermissions": "Roles & Permissions", "rolesPermissionsTransferEmpty": "There is no available role to transfer.", "noteDeleteRole1": "Those currently holding the position of", "noteDeleteRole2": "will be reassigned to the position of", "noteRole": "Note", "pause": "Pause", "pauseMsg": "Do you want to pause this voucher?", "resume": "Resume", "resumeMsg": "Do you want to resume this voucher?", "voucherCalendar": "Voucher calendar", "markComplete": "Mark Complete", "markCompleteMsg": "Do you want to complete?", "onlyThisVoucher": "Only this voucher", "allFutureVouchers": "All future vouchers", "voucherListView": "List view", "voucherCalendarView": "Calendar view", "oneTime": "One-time", "automated": "Automated", "repeatMsg": "{title}", "@repeatMsg": {"description": "title", "placeholders": {"title": {"type": "String"}}}, "weekly": "weekly", "daily": "daily", "notEnoughCredits": "Not enough credits", "voucherDelivered": "Delivered", "voucherEmail": "Email", "smsVoucher": "Sms", "pushVoucher": "<PERSON><PERSON>", "failed": "Failed", "opened": "Opened", "emailsClicked": "Emails Clicked", "creditsUsed": "Credits used", "voucherDate": "Voucher date", "every": "Every", "repeatOn": "Repeat on", "endsOn": "Ends on", "alert": "<PERSON><PERSON>", "claimWindow": "Claim time", "repeatAfterCompletion": "Repeats", "day": "Day", "year": "Year", "atTimeOfEvent": "At time of event", "fiveMinutesBefore": "5 minutes before", "fifteenMinutesBefore": "15 minutes before", "thirtyMinutesBefore": "30 minutes before", "oneHourBefore": "1 hour before", "twoHourBefore": "2 hour before", "on": "on", "and": "and", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "viewBy": "View by", "markCompleteOneMsg": "Are you sure you want to mark this voucher as complete?", "anytime": "Anytime", "endDateRequired": "End time must be greater than start time", "applyChangesTo": "Apply changes to:", "thisVoucher": "This Voucher", "thisVoucherSub": "Apply to the voucher only", "allVoucher": "All Voucher", "allVoucherSub": "Apply to this and future voucher", "claimTimeOptions": "Claim time", "voucherDateTitle": "Voucher date", "repeatAfterCompletionTitle": "Repeats", "repeatEndDay": "Ends on", "claimTimeOptionsSubTitle": "Decide when to claim the voucher. The default is Anytime, allowing claims at any moment. Choose other options for specific times.", "maximumQuantityIsRequired": "Maximum quantity is required", "redeemDateRequired": "Redeem start time must be equal to or greater than the claim start time", "never": "Never", "helpMeChoose": "Help me choose", "aiPoweredFeatureThatHelpsCustomers": "AI-powered feature that helps customers pick items they like through chat or recommendations.", "enableDisableHelpMeChoose": "Enable/Disable help me choose", "enableHelpMeChoose": "Enabled", "disabledHelpMeChoose": "Disabled", "dataHelpMeChoose": "Data Setup", "importData": "Import Data", "configureTheInputData": "Configure the input data that guides AI learning and predictions.", "helpMeChooseDataEmpty": "There is currently no data available, would you like to create some now?", "importAImageFile": "From Image", "importATextFile": "Text File (.csv, .txt)", "manualTextInput": "Manual Text Input", "dataPreview": "Data Preview", "fileUploadFailed": "Upload failed", "uploading": "Uploading", "wouldYouLikeToReplaceAllTheCurrentContent": "Would you like to add the new data to the existing data or replace the existing data?", "hintTextImportDataHelpMeChoose": "Enter any information you'd like, suggest by name, interest, or description, etc...", "leave": "Leave", "dataProcessing": "Data processing", "insertData": "Insert Data", "replaceData": "Replace Data", "accessToPhotosHasNotBeenGrated": "Access to photos has not been granted", "youNeedToGrantPermissionToAccessThePhotoLibrary": "You need to grant permission to access the photo library.", "accessToCameraHasNotBeenGrated": "Access to camera has not been granted", "youNeedToGrantPermissionToAccessTheCamera": "You need to allow access to the camera in order to proceed.", "goToSettings": "Go to Settings", "processingDataPleaseWait": "Processing data, please wait ...", "allowUserToGetHelp": "Allow user to get help?", "requestName": "Request name", "requestIcon": "Request icon", "widgetAppearance": "Widget Appearance", "reorderServices": "Reorder Services", "reorderTables": "Reorder Tables", "multipleHomeEngagementSubtitle": "A summary table of indicators helps manage the store's performance.", "multipleHomeCustomerSubtitle": "Quickly look up customer information, create new accounts, or give rewards.", "multipleHomeStickyQRSubtitle": "Quickly assign points, tag specific target customers, and support printing QR labels.", "multipleHomePointClaimsQRSubtitle": "Allows users to initiate requests for points by scanning static QR codes.", "multipleHomeCallButtonQRSubtitle": "Integrate call button for swift, polished customer assistance.", "multipleHomeVouchersCalendarSubtitle": "Displays detailed information about vouchers for each day, making management easier.", "multipleHomeFeatureEmpty": "There's no data to show right now!", "enterPageName": "Enter home name", "getHelp": "Get Help", "cart": "<PERSON><PERSON>", "enableShoppingCart": "Enable Shopping Cart", "allowAiToSendSupportRequest": "Allow AI to send support requests when customers ask for help.", "enableTheShoppingCartForAI": "Enable the shopping cart for AI to assist customer, add items, and notify the business.", "background": "Background", "addFeatures": "Add Widget", "pointsRedeemed": "Points redeemed", "voucherRedeemed": "Vouchers redeemed", "voucherClaimed": "Vouchers claimed", "cbFloor": "1st floor", "cbFloorTable": "Table {table}", "@cbFloorTable": {"description": "table", "placeholders": {"table": {"type": "String"}}}, "setDefaultHome": "Set as default", "homeNameRequired": "Home name is required", "addWidget": "Widget", "msgShowInAppOnly": "Show in app only", "startAt": "Start at", "widgetsEmpty": "No widgets are available to operate", "integrations": "Integrations", "featurePreview": "Feature Preview", "shareCopied": "<PERSON>pied", "manageTablesServices": "Manage Tables & Services", "additionalFeatures": "Additional Features", "helpMeChooseTitle": "Help Me Choose", "more": "more", "goToMyAccount": "Go to my account", "additionalButtons": "Additional Buttons", "thankYouVoucherCalendar": "Browse our promo calendar to claim your exclusive vouchers today!", "thankYouAnnouncementContent": "The description of the business announcement content will be displayed here.", "customerCheckin": "Customer Check-In", "allowCustomerCheckInEarnPointAndMore": "Allow customers check in, earn points and more.", "allowCustomerCheckIn": "Allow Customers Check-In", "enterLabelName": "Enter label name", "buttonName": "Button name", "enabledCheckin": "Enabled", "disabledCheckin": "Disabled", "earnAdditionalPoints": "Earn additional points?", "earnAdditionalPointsSubTitle": "Customers will receive points upon check-in, and the \"Give Points\" button will appear in the manager app for that table.", "checkedIn": "Checked In", "searchingForInformationPleaseWait": "Searching for information, please wait ...", "noUsersHaveCheckedIn": "No users have checked in", "balance": "Balance", "instrutionHeaderList": "Customize multiple screen", "instrutionHeaderListSub": "Tap here to manage and customize multiple screen.", "instrutionHeaderAdd": "Add new screen", "instrutionHeaderAddSub": "Tap here to create custom widgets according to your preferences.", "instrutionHeaderAddHomeName": "Home name", "instrutionHeaderAddHomeNameSub": "The desired home screen name to facilitate easy management.", "instrutionHeaderAddWidget": "Add Widget", "instrutionHeaderAddWidgetSub": "Tap here to open the widget panel with all the features you need.", "instrutionHeaderAddWidgetFirsr": "Add utility features", "instrutionHeaderAddWidgetFirsrSub": "Tap here to add the useful features you want to display on the home screen.", "instrutionHeaderAddColor": "Color palette", "instrutionHeaderAddColorSub": "Tap here to open the color palette for the home screen background.", "instrutionHeaderAddColorBackground": "Background", "instrutionHeaderAddColorBackgroundSub": "Tap to select your favorite color.", "instrutionHeaderArrange": "Remove", "instrutionHeaderArrangeSub": "Remove widget here.", "instrutionHeaderWidgetArrange": "<PERSON><PERSON><PERSON>", "instrutionHeaderWidgetArrangeSub": "You can touch and hold a widget to move it when there are multiple widgets.", "instrutionHeaderWidgetDoneSub": "Finally, tap here to complete.", "instrutionButtomSheetHeader1": "Hey, You Can Now", "instrutionButtomSheetHeader2": "Manage & Customize", "instrutionButtomSheetHeader3": "Multiple Screen", "instrutionButtomSheetContent1": "Personalize Your Home Screen", "instrutionButtomSheetContent1Sub": "Customize widgets, layouts, and colors to match your store's unique style, creating a convenient interface.", "instrutionButtomSheetContent2": "Multiple Home Screens", "instrutionButtomSheetContent2Sub": "Set up and switch between home screens for different purposes.", "instrutionButtomSheetContent3": "Smart Widgets Integration", "instrutionButtomSheetContent3Sub": "Add widgets for engagement counts, point redemption, <PERSON>y, Call Button, Vouchers, customer info, and more.", "instrutionEnableSub": "Tap to enable or disable the home screen.", "setAsDefaultMsg": "Do you want to set this as the default home screen?", "homeEmptyOwner": "No widgets yet. Tap the customize icon to add one.", "homeEmptyStaff": "No widgets available at the moment.", "letUsKnowYoureHere": "Let us know you’re here!", "checkIn": "Check-In", "helpAndSupport": "Help & Support", "getStickyQRSupport": "Get StickyQR Support", "termsAndPrivacy": "Terms & Privacy", "helpWith": "Help with", "callButtonOptions": "Call Button settings", "tooltipDisable": "The home screen is set as default, making this function unavailable.", "pressAndHold": "Press and hold to move widget position.", "aiSummary": "AI Summary", "unableToFindCustomerInfomation": "Unable to find customer information.", "sendCustomerAnnouncements": "Notice to Customers", "sendCustomerAnnouncementsSub": "Notify customers via App Push or Email by selecting the options below.", "appPush": "App PUSH (StickyQR app)", "emailPush": "E-mail", "subject": "Subject", "emailSubjectIsRequired": "Subject is required", "annOffWarning": "The announcement is off. Customers will not receive notifications.", "enableNow": "Enable Now", "createAIAnnouncement": "Create AI Announcement", "createAIAnnouncementDialogMsg": "Would you like to use AI to create an announcement to notify customers about this voucher?", "pro": "Pro", "moreMenuTitle": "More", "hide": "<PERSON>de", "featuresMenu": "features", "featureMsg": "Feature", "standard": "Standard", "enterprise": "Enterprise", "upgrade": "Upgrade", "showMore": "Show more", "showLess": "Show less", "buyCreditBilling": "Buy credit", "planStandard": "Standard", "planPro": "Pro", "planEnterprise": "Enterprise", "setupPrinter": "Setup printer", "retryingPrint": "Retrying print...", "retryingPrintAgain": "Still unable to print, please check your printer again", "printingFailed": "Printing failed, please try again after {durationTime}s...", "@printingFailed": {"description": "durationTime", "placeholders": {"durationTime": {"type": "String"}}}, "printingLastFailed": "Still unable to print, final attempt in {durationTime}s...", "@printingLastFailed": {"description": "durationTime", "placeholders": {"durationTime": {"type": "String"}}}, "setUpPrinter": "Setup", "newOrders": "New", "newOrder": "New", "newScheduleOrder": "New, Scheduled", "inProgressOrder": "In progress", "readyOrder": "Ready", "scheduledOrder": "Scheduled", "lengthTotalItems": "{count} TOTAL ITEM{s}", "@lengthTotalItems": {"description": "count", "placeholders": {"count": {"type": "String"}, "s": {"type": "String"}}}, "unavailable": "Unavailable", "unavailableToday": "Unavailable today", "unavailableIn3Hours": "Unavailable in 3 hours", "categories": "Categories", "allItems": "All items", "searchForAnItem": "Search for an item", "orderStatus": "Status: {status}", "@orderStatus": {"description": "status", "placeholders": {"status": {"type": "String"}}}, "subtotal": "Subtotal", "tax": "Tax", "markInProgress": "Mark in progress", "markReady": "Mark ready", "markCompleted": "Mark completed", "issueWithOrder": "Issue", "noOrdersyet": "No orders yet", "tapToViewAndConfirm": "Tap to view and confirm", "orderScheduledStatus": "Scheduled", "orderNewStatus": "New Order{s}", "@orderNewStatus": {"description": "count", "placeholders": {"s": {"type": "String"}}}, "specialRequirements": "Special requirements", "pickupAt": "Pickup at {time}", "@pickupAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "itemsOrder": "{count} item{s}", "@itemsOrder": {"description": "s", "placeholders": {"count": {"type": "int"}, "s": {"type": "String"}}}, "remindMe": "Remind me", "managementProducts": "Management Products", "exitOrdering": "Exit", "readyIn": "Ready in", "now": "Now", "expired": "Expired", "ordersHistory": "Order History", "odHistoryOrderPickedUp": "Order picked up", "odHistoryOrderDetail": "Order detail", "selectedAnOrderToViewDetail": "Select an order to view details", "odHistoryIssueRefund": "Issue Refund", "odHistoryConfirmRefund": "Confirm Refund", "odHistoryTotalItems": "Total items", "odHistoryTotalItem": "Total item", "odHistorySubtotal": "Subtotal", "odHistoryTax": "Tax", "odHistoryTotal": "Total", "odIssueRefundItem": "<PERSON><PERSON>", "odIssueRefundAmountTitle": "Amount", "odIssueRefundAmount": "Refund amount", "odIssueRefundItems": "Refund {itemsCount} items", "@odIssueRefundItems": {"description": "itemsCount", "placeholders": {"itemsCount": {"type": "String"}}}, "items": "Items", "modifiers": "Modifiers", "searchModifiers": "Search Modifiers", "searchCategories": "Search Categories", "noCategoriesYet": "No categories yet.", "noItemsYet": "No items yet.", "noModifiersYet": "No modifiers yet.", "allModifiers": "All modifiers", "odIssueRefundRefundTo": "Refund to", "odIssueRefundReasonForRefund": "Reason for Refund?", "odIssueRefundMaximumRefundAmountIs": "Maximum refund amount is", "odIssueRefundAmountToRefund": "Amount to refund", "odIssueRefundEnterAmountToRefund": "Enter amount to refund", "odIssueRefundMaximumIs": "Maximum is", "settingsOrders": "Eat.chat AI Ordering Settings", "autoConfirmNewOrder": "Auto-confirm new order", "storeHours": "Store hours", "orderAlert": "Order Alert", "orderReceiptPrinters": "Receipt Printers", "specialHours": "Special Hours", "regularOpeningHours": "Regular Opening Hours", "theseAreHoursYourStoreIsAvailable": "These are hours your store is available", "specialHoursAndClosures": "Special Hours and Closures", "addSpecialHours": "Add special hours", "orderSoundSettings": "Sound settings", "orderNotificationSound": "Notification sound", "silent": "Silent", "areYouSureYouWantToRefund": "Are you sure you want to refund?", "refundEntireOrderRemainingBalance": "Refund entire order / remaining balance", "youDontHaveAnyOrderHistoryYet": "You don't have any order history yet!", "issueWithOrderActive": "Issue", "outOfStock": "Out of stock", "adjustPrepTime": "Adjust Prep Time", "cancelOrder": "Cancel Order", "whyDoYouWantToCancelTheOrder": "Why do you want to cancel the order?", "storeIsTooBusy": "Store is too busy", "theStoreIsClosing": "The store is closing", "productIsOutOfStock": "Product is out of stock", "needMorePrepTime": "Need more prep time", "needMorePrepTimeMsg": "Need more time to prepare this order? You can adjust the pickup time from the order details view by tapping the -5 or +5 buttons.", "gotIt": "Got it", "pleaseStateReason": "Please state reason", "noKeepThisOrder": "No, keep this order", "yesCancelThisOrder": "Yes, cancel this order", "tellCustomersWhy": "Tell customers why", "noItemSelected": "No item selected", "selectedItems": "Selected item", "chooseItemOutOfStock": "Choose item out of stock", "howLongWillItTakeToBeBackInStock": "How long will it take to be back in stock?", "howWouldYouLikeToHandleTheOrderIssue": "How would you like to handle the order issue?", "refundForThisItem": "Refund for this item", "replaceTheItem": "Replace the item", "refundConfirmation": "Refund Confirmation", "commingSoon": "Comming soon", "in4Hours": "In 4 hours", "endOfToday": "End of today", "indefinitely": "Indefinitely", "ultil": "Until", "untilManuallyReactivated": "Until manually reactivated", "productsManage": "Products", "activeOrders": "Active Orders", "stickyOrders": "EAT.CHAT AI ORDERING", "confirmation": "Confirmation", "noRemindMe": "No", "confirmScheduleProgress": "You are fulfilling an order **{time}** earlier than the scheduled time. Are you sure you want to fulfill this order right now.", "@confirmScheduleProgress": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "days": "day{s}", "@days": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "hours": "hour{s}", "@hours": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "minutes": "minute{s}", "@minutes": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "acceptOnlineOrdering": "Accept Online Ordering", "yesterday": "Yesterday", "refunded": "Refunded", "updateStoreStatus": "Update store status", "normal": "Normal", "busy": "Busy", "acceptingOrders": "Accepting orders", "morePrepTime": "More prep time", "noNewOrders": "No new orders", "yourStoreIsAvailableAndAcceptingOrders": "Your store is available and accepting orders", "customersCanPlaceOrdersWithinStandardTimesOnTheStoreOrderingChannels": "Customers can place orders within standard times on the store's ordering channels.", "changeStatus": "Change status", "howMuchAdditionalPrepTimeDoYouNeed": "How much additional prep time do you need?", "prepTime": "prep time", "howlongWouldYouLikeToPauseNewOrders": "How long would you like to pause new orders?", "pauseFor": "Pause for", "pauseForEndOfToday": "Pause for", "customersWillNoLongerBeAbleToPlaceOrdersOnTheStoreOrderingChannels": "Customers will no longer be able to place orders on the store's ordering channels.", "weAddMinutesToYourEstimatedOrder": "We'll add {time} minutes to your estimated order completion time on your online store and ordering chat app", "@weAddMinutesToYourEstimatedOrder": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "itWillReturnToNormalPrepTimeAfterMin": "It will return to normal prep time after {time}.", "@itWillReturnToNormalPrepTimeAfterMin": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "min": "min", "mins": "mins", "updateStoreHours": "hours", "acceptingOrdersUntil": "Accepting orders until", "pauseAcceptingOrdersUntil": "Pause accepting orders until", "updateStoreEndOfToday": "End of today", "storesWillPauseAndStopReceivingOrders": "Stores will pause and stop receiving orders", "customersWillTemporarilyBeUnableToPlaceOrdersOnTheOnlineStoreAndOrderingChatApp": "Customers will temporarily be unable to place orders on the online store and ordering chat app.", "theStoreWillReturnToNormalOperationsAfter": "The store will return to normal operations after {time}.", "@theStoreWillReturnToNormalOperationsAfter": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "theStoreIsTemporarilyPausingOrderTakingUntilTheEndOfToday": "The store is temporarily pausing order taking until the end of today.", "allowOnlineOrdering": "Allow Online Ordering", "whenTurnedOffTheStoreWillRemainClosedUntilTurnedBackOn": "When turned off, the store will remain closed until turned back on.", "closed": "Closed", "needsAction": "Needs action", "undo": "Undo", "undoMsg": "You have changed the status from **{status1}** to **{status2}**. Do you want to undo?", "@undoMsg": {"description": "msg", "placeholders": {"status1": {"type": "String"}, "status2": {"type": "String"}}}, "unavailableTodayCategory": "Unavailable Today Category", "unavailableCategory": "Unavailable Category", "availableCategory": "Available Category", "availableCategoryMsg": "You can edit your items, but they'll be hidden until you make this category available.", "orders": "Orders", "ordersWidgetMsg": "The Active Orders feature allows staff to track and manage orders in real-time, update statuses, and process them efficiently.", "returned": "Returned", "canceled": "Cancelled", "foc": "FOC", "refund": "Refund", "theAvailableRefundAmountForThisOrderIs": "The available refund amount for this order is {amount}.", "@theAvailableRefundAmountForThisOrderIs": {"description": "amount", "placeholders": {"amount": {"type": "String"}}}, "thisOrderHasBeenFullyRefunded": "This order has been fully refunded.", "willYouBePpenOrClosed": "Will you be open or closed?", "storeOpen": "Store open", "storeClose": "Store close", "selectDates": "Select dates", "open": "Open", "selectTime": "Select time", "hour": "Hour", "minute": "Minute", "storeAvailability": "Store Availability", "editSpecialHours": "Edit special hours", "openAllDay": "Open all day", "addSpecialHoursOrClosuresForHolidays": "Add special hours or closures for holidays, special events, or other exceptional events. This will temporarily replace your regular menu hours.", "editRegularOpeningHours": "Edit regular opening hours", "stickyPointsTitle": "Sticky Points", "integrationStickyPointsSubTitle": "Build long-term relationships with customers, Encourage return visits with tailored rewards and exclusive access to deals and events.", "stickyVouchersTitle": "Sticky Vouchers", "stickyVouchersSubTitle": "Use digital vouchers and coupons to increase engagement and streamline customer experience.", "enableStickyPointsTitle": "Enable Sticky Points", "enableStickyPointsSubTitle": "Enabled the 'Sticky Points' feature to advertise to attract more members to accumulate points and redeem rewards at the store.", "pointConversionTitle": "Point Conversion", "pointConversionSubTitle": "Choose an amount of points to be given in your currency (for example, 1 point per USD, 1 point per 10.000 VND, etc.).", "pointRoundingOffTitle": "Point Rounding-off", "pointRoundingOffSubTitle": "Choose how to round off loyalty points for order totals that are odd, either to the nearest integer (7.4 becomes 7) or to the next integer (7.4 becomes 8 - default)", "maximumRewardsPerOrderTitle": "Maximum Rewards per order", "maximumRewardsPerOrderSubTitle": "Allow maximum 1 or multiple rewards that can be applied for each order. Default is max 1 reward per order.", "nearestInteger": "Nearest integer", "nextInteger": "Next integer", "maximumOneReward": "Maximum one reward", "multipleRewardsAllowed": "Multiple rewards allowed", "comingSoon": "Coming soon...", "discounts": "Discounts", "amountIsRequired": "Amount is required", "percentageIsRequired": "Percentage is required", "selectProducts": "Select products", "searchProducts": "Search products", "noProductsFound": "No products found", "selectCategories": "Select categories", "noCategoriesFound": "No categories found", "rewardTypeHeader": "Integrate Sticky Points with Eat.chat AI ordering (Optional).", "rewardTypeTitle": "Choose how rewards apply to the order.", "maxValueRewardTitle": "Maximum value (to be credited towards the order total)", "noMax": "(no max)", "productCategories": "Product categories", "excludeProducts": "Exclude products", "doYouWantToIntegrateStickyPoints": "Do you want to integrate Sticky Points with Eat.chat AI ordering?", "doYouWantToIntegrateStickyPointsSubtitle": "Integration allows customers to earn points and redeem for rewards whenever they place an online order.", "takeMeThere": "Yes, take me there", "productsAreRequired": "Products are required", "categoriesAreRequired": "Categories are required", "chooseRewardType": "Choose reward type", "percentage": "Percentage", "enterPercentage": "Enter percentage", "myRewardsVouchers": "My Rewards & Vouchers", "orderOnline": "Order Online", "referAndEarnMore": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>", "selectTags": "Select tags", "noTagsFound": "No tags found", "download": "Download", "downloadSubtitle": "Get special offers available in app only", "myAccount": "Go to my account", "september2025": "September 2025", "showMoreTKPage": "Show more", "enhanceWithAI": "Enhance with AI", "enterYourKeyword": "Enter your keyword...", "keyword": "Keyword", "use": "Use", "announcementsWitchInactive": "Set the auto-time for the announcement to switch to inactive.", "dialogOrderOnline": "You cannot apply this selection yet because the online ordering feature is not enabled. Would you like to enable this feature in the settings?", "skipNotifyingCustomers": "(Skip notifying customers)", "selectDate": "Select date", "generateAnn": "Generate", "autoReadyOrder": "Auto-ready for orders in progress", "online": "Online", "serviceFee": "Platform fee", "orderTimeline": "Order timeline", "pickedUp": "Picked Up", "details": "details", "readyTime": "Ready time", "completedTime": "Completed time", "cancelTime": "Cancelled time", "preparingTime": "Preparing time", "scheduledTime": "Scheduled time", "orderReadyTime": "Order ready time", "orderCompletedTime": "Order completed time", "orderCancelTime": "Order cancelled time", "orderPreparingTime": "Order preparing time", "orderScheduledTime": "Order scheduled time", "yesRegister": "Yes, Register", "registerCustomerHaveAccTitle": "This phone number is associated with a user account.", "registerCustomerHaveAccMsg": "Do you want to use it to register a business?", "enterYourAddress": "Enter your address", "businessAdressRequired": "Address is required", "enterYourBusinessName": "Enter your business name", "stickyAISupport": "StickyQR AI Support – Instant help, anytime", "enterTextstickyAISupport": "Search, type or say anything...", "errorMsgStickyAISupport": "An error has occurred. Please try again...", "rewardRedeemed": "Rewards redeemed", "reviewUsOnGoogleTotal": "Review us on Google", "reviewUsOnGoogleSubtotal": "Allow customers to share their experience through a Google review.", "callButtonOrderItem": "<PERSON><PERSON>", "callButtonOrderItems": "Items", "orderTime": "Order time", "autoCompleteOrder": "Auto-complete", "automaticallyClearsAllReadyOrdersAt": "Automatically clears all ready orders at:", "automaticallyClearsNote1": "The auto-complete time you selected falls within business hours. This may affect orders that are still being processed.", "automaticallyClearsNote2": "It’s recommended to set this at least 1 hour after closing time.", "orderMore": "Order More", "orderMoreSubtotal": "Order more items through the ordering system.", "useOnlineMenu": "Use online menu", "availableCategories": "Available categories", "tipNote": "Tip: ", "orderMoreTitle": "Order more", "orderMoreSubtitle": "Enable feature allowing customers to order food easily and efficiently.", "categoriesNoProductsFound": "No products found in the selected categories.", "payAtStore": "Pay at Store", "unPaid": "Unpaid", "managePayment": "Manage Payments", "payByCard": "Pay by Card", "payByCash": "Pay by Cash", "payByApplePay": "Apple Pay", "payByGooglePay": "Google Pay", "collectCash": "Are you sure you want to mark this order as paid in cash?", "cardPayments": "Card Payments", "cash": "Cash", "markAsPaidInCash": "<PERSON> as <PERSON><PERSON> in Cash", "sendPaymentLinkTitle": "Send Payment Link", "editContent": "Edit content", "startOver": "Start over", "startOverSub": "(Reset to default)", "autoConfirm": "Auto Confirm", "autoReady": "Auto Ready", "completedAt": "Completed at {time}", "@completedAt": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "shareTableURLLink": "Share Table URL link", "shareQRCodeImage": "Share QR Code Image", "orderTimeAtive": "Order time {time}", "@orderTimeAtive": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "orderName": "Order:", "orderDate": "Date:", "range": "Range:", "last7Days": "Last 7 days", "last30Days": "Last 30 days", "last3Months": "Last 3 months", "last6Months": "Last 6 months", "monthToDate": "Month to date", "atRiskCustomers": "At-Risk Customers", "championCustomers": "Champion Customers", "hibernatingCustomers": "Hibernating Customers", "lostCustomers": "Lost Customers", "loyalCustomers": "Loyal Customers", "newCustomers": "New Customers", "potentialLoyalist": "Potential Loyalists", "reactivatedCustomers": "Reactivated Customers", "unlimitedUsage": "Unlimited usage", "orderDelivery": "Delivery", "orderDeliveryDescription": "Customers can choose delivery at checkout, complete payment securely, and track their order status.", "orderDeliveryWarning": "Payment gateway required for delivery.", "delivery": "Delivery", "targetedOffers": "Targeted Offers", "duration": "Duration", "added": "Added", "durationContent": "Expires in {duration}", "@durationContent": {"description": "duration", "placeholders": {"duration": {"type": "String"}}}, "noResultsFound": "No results found!", "targetOffersEmpty": "There's no data to show right now!", "offerPerformance": "Offer performance", "claimRate": "Claim rate", "redemptionRate": "Redemption rate", "maxPerUserOffer": "Max per user", "targetedOfferDetailTitle": "Targeted offer detail", "rfmCustomerSegments": "RFM Customer Segments", "rfmCustomerSegmentsSubtitle": "(Recency, Frequency, Monetary)", "recency": "Recency", "frequency": "Frequency", "daysRecency": "Day{s}", "@daysRecency": {"description": "s", "placeholders": {"s": {"type": "String"}}}, "atRiskCustomersSubTitle": "Once loyal, but haven't visited lately.", "championCustomersSubTitle": "Loyal, highly valuable customers.", "hibernatingCustomersSubTitle": "Haven't visited in a long time, likely lost customers.", "lostCustomersSubTitle": "Essentially inactive, no longer considered customers.", "loyalCustomersSubTitle": "Repeat customers who visit often.", "newCustomersSubTitle": "Newly acquired customers.", "potentialLoyalistSubTitle": "Recent customers with potential for loyalty.", "reactivatedCustomersSubTitle": "Customers who just returned after a period of inactivity.", "customerSegmentHistory": "Segments history", "dashboard": "Dashboard", "rfmCustomerSegmentsMenu": "RFM Customer Segments", "targetedOffer": "Targeted Offer", "segments": "Segments", "sendingTime": "Sending time", "firstOfferDay": "First offer - day", "secondOfferDay": "Second offer - day", "tooltipOfferFirst": "When a customer enters a new segment, up to 2 offers can be sent - on the first day, and a day of your choice (default 15).", "tooltipOfferAvailable": "Offer is ready to send, but won’t be delivered automatically unless scheduled or set to auto-send.", "claims": "claims", "impressions": "Impressions", "segment": "Segment", "estimatedCustomers": "Estimated customers", "estimatedSpending": "Estimated spending", "deliveryResults": "Delivery results", "inApp": "In-app", "upcomingIn24h": "Upcoming in 24h", "dailyUpcomingSends": "Daily Upcoming Sends ({segment})", "@dailyUpcomingSends": {"description": "segment", "placeholders": {"segment": {"type": "String"}}}, "upcomingEmpty": "No upcoming offers scheduled for today.", "currentCustomerSegments": "Current Customer Segments", "smsWarning": "SMS channel not running due to insufficient credits.", "unableToSendSMS": "Unable to Send SMS", "upcomingTOTitle": "Upcoming", "channelPerformance": "Channel Performance", "channels": "Channels", "claimsChannels": "<PERSON><PERSON><PERSON>", "allSegments": "All Segments", "courierFound": "Courier Found!", "estimatedArrival": "Estimated arrival {time}", "@estimatedArrival": {"description": "time", "placeholders": {"time": {"type": "String"}}}, "courier": "Courier", "assigningCourier": "Assigning Courier...", "onTheWay": "On the way", "deliveredOrder": "Delivered", "customerInformation": "Customer Information", "deliveryDetails": "Delivery details", "deliveryAddress": "Delivery address", "deliveryFee": "Delivery fee", "customerRefusedDelivery": "Customer refused delivery", "cannotFindCustomer": "Cannot find customer", "deliveryTracking": "Delivery tracking", "failedOrder": "Failed", "canceledOrder": "Canceled", "deliveryStatus": "Delivery status", "chooseTicketFontSize": "Choose Ticket Font <PERSON>", "kitchenTicketSmall": "Small", "kitchenTicketDefault": "<PERSON><PERSON><PERSON>", "kitchenTicketLarge": "Large", "receiptCustomerName": "Customer Name", "receiptPickupTime": "Pickup Time", "receiptOrderNumber": "Order Number", "receiptBusinessInfo": "Business information", "receiptLogoPrinted": "Printed logos are converted to black and white. Please print test your receipt to confirm actual print quality", "receiptContact": "Contact", "receiptOrderInfo": "Order information", "receiptAdditionalText": "Additional text", "receiptCustomText": "Custom Text", "receiptCustomTextExample": "E.g: \"Thank you!\"", "noReceiptFound": "No receipt found!", "printersReceipt": "Printers & Receipt", "customizeReceipts": "Customize receipts", "customizeKitchenTicket": "Kitchen", "shippingFeeWillBeRefunded": "Shipping fee will be refunded", "customerTargetedOffersIntegrationsSubtitle": "Personalized promotions designed for specific customer segments to maximize engagement, loyalty, and revenue.", "feeOff": "OFF", "feeFlatRate": "Flat rate", "feeFreeDelivery": "FREE delivery", "deliveryPoweredByUberDirect": "Delivery Powered by Uber Direct", "deliveryPoweredByUberDirectSub": "We've partnered with Uber Direct to offer seamless delivery for your customers.", "deliverySettings": "Delivery Settings", "unitedStatesUberPricing": "United States Uber Pricing", "pricingUberInCalifornia": "*Pricing in California is increased by +$2 to account for Prop-22.", "miles": "miles", "averageDelivery": "Average Delivery Time: 20-35 minutes", "uberHandlesAllDispatching": "No Driver Tips Required - Uber handles all dispatching", "customersReceiveRealTimeTrackingLinks": "Live Tracking: Customers receive real-time tracking links", "youCanOfferFreeDiscountedDelivery": "You can offer free or discounted delivery to customers by setting rules in your StickyQR Manager App (e.g., free delivery over $150 subtotal).", "deliveryFeeSupport": "Delivery Fee support", "deliveryFeeSupportSub": "Offer free or discounted delivery based on subtotal (after discounts): Automatically apply $X off or waive delivery fee when the order subtotal exceeds $Y. Choose Flat Rate if applicable (E.g. Minimum order subtotal $30 → delivery fee is $6.99)", "subtotalMinium": "Subtotal minium($)", "addAnotherTier": "Add another tier", "lastChange": "Last change", "sevenDays": "7 Days", "fourWeeks": "4 Weeks", "segmentedCustomers": "Segmented Customers", "totalTransactions": "Total Transactions", "newTransactionsIn24Hours": "New Transactions In 24 Hours", "totalTransactionsChart": "Total Transactions {data}", "@totalTransactionsChart": {"description": "data", "placeholders": {"data": {"type": "String"}}}, "labels": "Labels", "receipt": "Receipt", "receipts": "Receipts", "printerDPI": "Printer DPI", "paperSize": "Paper size", "lastWeek": "Last week", "lastMonth": "Last month", "printerStatus": "Printer Status", "receiptCopiesPerOrder": "Receipt copies per order", "kitchenTicket": "Kitchen ticket", "categoriesToPrint": "Categories to print", "automaticallyPrint": "Automatically Print In Progress Orders", "allCategories": "All categories", "noPrinterConnected": "No printer connected, please set up printer", "here": "here", "hardware": "Hardware", "showModifiers": "Show Modifiers", "both": "Both", "printAll": "Print all", "kitchenTicketOnly": "Kitchen ticket only", "receiptOnly": "Receipt only", "options": "Options", "invoice": "Invoice", "receiptPickup": "Pickup", "confirmedBySystem": "Confirmed by System", "kitchenStaff": "Staff"}