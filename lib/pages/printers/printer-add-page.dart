// ignore_for_file: empty_catches

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_network_ios/flutter_local_network_ios.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/labels-local/labels-local.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-discovery/printers_discovery.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-tabs/printers-tabs.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers/printers.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers/update-printer.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/printers/@printer-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/kitchen-ticket-template.dart';

class PrinterAddPage extends StatelessWidget {
  const PrinterAddPage({super.key});

  @override
  Widget build(BuildContext context) {
    final args = ModalRoute.of(context)?.settings.arguments;
    Printer? printer;
    bool isEdit = false;
    bool isReceipt = false;
    int index = -1;
    if (args is ScreenArguments) {
      if (args.data is Printer) {
        printer = args.data as Printer;
        index = int.parse(args.type ?? '-1');
        isReceipt = printer.isReceipt ?? false;
        isEdit = true;
      } else {
        if (args.data is PrintersTab) {
          final tab = args.data;
          isReceipt = tab == PrintersTab.Receipt;
        }
      }
    }

    return BlocProvider(
      create: (context) => PrintersDiscoveryCubit(),
      child: PrinterAddContent(
        isEdit: isEdit,
        isReceipt: isReceipt,
        printer: printer,
        index: index,
      ),
    );
  }
}

class PrinterAddContent extends StatefulWidget {
  final bool isEdit;
  final bool isReceipt;
  final bool isScanPrinter;
  final Printer? printer;
  final int index;
  const PrinterAddContent({
    super.key,
    required this.isEdit,
    required this.isReceipt,
    this.printer,
    this.isScanPrinter = false,
    this.index = -1,
  });

  @override
  State<PrinterAddContent> createState() => _PrinterAddContentState();
}

class _PrinterAddContentState extends State<PrinterAddContent> {
  @override
  void initState() {
    super.initState();
    if (Platform.isIOS) {
      checkPermissionLocalNetwok().then((bool checkPermission) {
        if (!checkPermission) {
          final l10n = context.l10n;
          AppBased.showDialogYesNo(
            context,
            title: l10n.confirm,
            noText: l10n.no,
            yesText: l10n.yes,
            msgContent: l10n.permissionLocalNetworkTitle,
            noTap: () {},
            yesTap: () {
              openAppSettings();
            },
            onChanged: (value) {
              AppLog.e('value:  $value');
            },
          );
        }
      });
    }
    final showScanPrintersManual = context.read<AuthBloc>().state.showScanPrintersManual;
    if (showScanPrintersManual != true) {
      context.read<PrintersDiscoveryCubit>().onDiscoveryPrinters();
    }
  }

  Future<bool> checkPermissionLocalNetwok() async {
    final result = await FlutterLocalNetworkIos().requestAuthorization() ?? false;
    return result;
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<PrinterCubit>(
          create: (_) => PrinterCubit()
            ..onChangedPrinter(widget.printer)
            ..onChangedIsEnable(widget.printer?.isEnable ?? true)
            ..onChangedIsReceipt(widget.isReceipt)
            ..onChangedName(widget.printer?.productName ?? '')
            ..onChangedIPAddress(widget.printer?.address ?? '')
            ..onChangedPort(widget.printer?.jsonPortNumber ?? 9100)
            ..onChangedID(widget.printer?.id ?? '')
            ..onChangedDPI(widget.printer?.dpi ?? '203dpi')
            ..onChangedPageSize(widget.printer?.pageSize ?? '58mm'),
        ),
        BlocProvider<LabelsLocalCubit>(
          lazy: false,
          create: (_) => LabelsLocalCubit()..doGetLabelsLocal(),
        ),
      ],
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          final showScanPrintersManual = state.showScanPrintersManual;
          // if (showScanPrintersManual != true) {
          //   context.read<PrintersDiscoveryCubit>().onDiscoveryPrinters();
          // }
          return PrinterAddWidget(
            isEdit: widget.isEdit,
            isReceipt: widget.isReceipt,
            printer: widget.printer,
            index: widget.index,
            isScanPrinter: showScanPrintersManual ?? false,
          );
        },
      ),
    );
  }
}

class PrinterAddWidget extends StatelessWidget {
  final bool isEdit;
  final bool isReceipt;
  final bool isScanPrinter;
  final Printer? printer;
  final int index;
  const PrinterAddWidget({
    super.key,
    required this.isEdit,
    required this.isReceipt,
    this.printer,
    this.isScanPrinter = false,
    this.index = -1,
  });

  @override
  Widget build(BuildContext context) {
    final themePage = context.read<AppThemesCubit>().printerTheme;
    final l10n = context.l10n;

    //TAM AN SCAN TREN IOS 16
    // bool isHiden = false;
    // try {
    //   if (Platform.isIOS && double.parse(Platform.operatingSystemVersion.split(' ')[1].split('.')[0]) >= 16) {
    //     isHiden = false;
    //   }
    // } catch (e) {}
    return Scaffold(
      appBar: _buildAppBar(
        context,
        isEdit ? l10n.editPrinter : l10n.addPrinter,
        printer,
        isEdit: isEdit,
      ),
      body: SafeArea(
        bottom: false,
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: BlocConsumer<PrinterCubit, PrinterState>(
            listener: (context, state) {
              switch (state.status) {
                case PrinterStatus.Error:
                  return;
                case PrinterStatus.Updated:
                  context.read<UpdatePrintersCubit>().onUpdatePrinter(state.id ?? '', state.printer);
                  return;
                case PrinterStatus.Success:
                  context.read<UpdatePrintersCubit>().onAddPrinter(state.printer);
                  return;
                default:
              }
            },
            builder: (context, state) {
              return SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: Container(
                  margin: const EdgeInsets.only(bottom: 24),
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(bottom: 16),
                        padding: const EdgeInsets.fromLTRB(16, 8, 2, 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            width: 1,
                            color: AppColors.appBGGreyColor,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            _buildTitle(themePage, l10n.active),
                            Transform.scale(
                              scale: 0.7,
                              child: CupertinoSwitch(
                                value: state.isEnable,
                                inactiveTrackColor: AppColors.appBGGreyColor,
                                activeTrackColor: AppColors.cupertinoSwitchColor,
                                onChanged: (value) {
                                  context.read<PrinterCubit>().onChangedIsEnable(value);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(bottom: 16.0),
                        child: Focus(
                          onFocusChange: (hasFocus) {
                            context.read<PrinterCubit>().onChangedNameIndex(state.name.value.length);
                          },
                          child: PrinterInputWidget(
                            labelText: l10n.printerName,
                            hintText: l10n.printerName,
                            isValidate: state.isValidateName,
                            initialValue: state.name.value,
                            index: state.nameIndex,
                            keyboardType: TextInputType.text,
                            errText: state.name.error == ValidateControlStatus.empty ? l10n.printerNameRequired : '',
                            onChanged: (String value) {
                              context.read<PrinterCubit>().onChangedName(value);
                            },
                            onChangedIndex: (int value) {
                              context.read<PrinterCubit>().onChangedNameIndex(value);
                            },
                          ),
                        ),
                      ),
                      if (state.isReceipt == true || isReceipt == true) ...[
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: dropDownPageSize(
                            context,
                            state,
                            value: state.pageSize?.value,
                            l10n.paperSize,
                            list: [
                              '58mm',
                              '80mm',
                            ],
                            hint: l10n.size,
                            onChanged: (value) {
                              context.read<PrinterCubit>().onChangedPageSize(value);
                            },
                          ),
                        ),
                      ] else ...[
                        Padding(
                          padding: const EdgeInsets.only(bottom: 16.0),
                          child: dropDownDPI(
                            context,
                            state,
                            value: state.dpi.value,
                            l10n.printerDPI,
                            list: [
                              '203dpi',
                              '300dpi',
                            ],
                            hint: l10n.dpi,
                            onChanged: (value) {
                              context.read<PrinterCubit>().onChangedDPI(value);
                            },
                          ),
                        ),
                      ],
                      if (!isScanPrinter)
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            border: Border.all(
                              width: 1,
                              color: AppColors.appBorderColor,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 16,
                                ),
                                decoration: const BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      width: 1,
                                      color: AppColors.appBorderColor,
                                    ),
                                  ),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      l10n.wifiEthernetPrinters,
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: AppColors.darkPrimaryBackgroundColor,
                                      ),
                                    ),
                                    BlocBuilder<PrintersDiscoveryCubit, PrintersDiscoveryState>(
                                      builder: (context, state) {
                                        return ButtonLoading(
                                          height: 36,
                                          textDirection: TextDirection.rtl,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600,
                                          labelColor: AppColors.darkPrimaryBackgroundColor,
                                          circularStrokeColor: AppColors.darkPrimaryBackgroundColor,
                                          label: l10n.scan,
                                          isLoading: state.status == PrintersDiscoveryStatus.Loading ? true : false,
                                          borderRadius: 8,
                                          buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 24,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(8.0),
                                            side: const BorderSide(
                                              color: AppColors.primaryBackColor,
                                            ),
                                          ),
                                          callback: () {
                                            FocusScope.of(context).unfocus();
                                            if (Platform.isIOS) {
                                              checkPermissionLocalNetwok().then((bool checkPermission) {
                                                if (!checkPermission) {
                                                  final l10n = context.l10n;
                                                  AppBased.showDialogYesNo(
                                                    context,
                                                    title: l10n.confirm,
                                                    noText: l10n.no,
                                                    yesText: l10n.yes,
                                                    msgContent: l10n.permissionLocalNetworkTitle,
                                                    noTap: () {},
                                                    yesTap: () {
                                                      openAppSettings();
                                                    },
                                                    onChanged: (value) {
                                                      AppLog.e(
                                                        'value:  $value',
                                                      );
                                                    },
                                                  );
                                                } else {
                                                  context.read<PrintersDiscoveryCubit>().onDiscoveryPrinters();
                                                  AppBased.doAuditLogging(
                                                    pageName: 'PrinterAddPage',
                                                    pageUrl: 'printer-add-new',
                                                    pageAction: 'scanPrinter',
                                                  );
                                                }
                                              });
                                            } else {
                                              context.read<PrintersDiscoveryCubit>().onDiscoveryPrinters();
                                              AppBased.doAuditLogging(
                                                pageName: 'PrinterAddPage',
                                                pageUrl: 'printer-add-new',
                                                pageAction: 'scanPrinter',
                                              );
                                            }
                                          },
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              DecoratedBox(
                                decoration: const BoxDecoration(
                                  color: AppColors.lightPrimaryBackgroundColor,
                                  borderRadius: BorderRadius.vertical(
                                    bottom: Radius.circular(8),
                                  ),
                                ),
                                child: BlocBuilder<PrintersDiscoveryCubit, PrintersDiscoveryState>(
                                  builder: (context, statePrinters) {
                                    switch (statePrinters.status) {
                                      case PrintersDiscoveryStatus.Loading:
                                        final List<Printer> printers = statePrinters.printers ?? [];
                                        if (printers.isNotEmpty) {
                                          return Column(
                                            children: [
                                              _buildListPrinter(
                                                themePage,
                                                l10n,
                                                state,
                                                printers,
                                                state.name.value,
                                              ),
                                              Container(
                                                width: MediaQuery.of(context).size.width,
                                                padding: const EdgeInsets.all(16),
                                                child: Text(
                                                  l10n.scanning,
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.w400,
                                                    color: Colors.black.withValues(alpha: .6),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        }
                                        return Container(
                                          width: MediaQuery.of(context).size.width,
                                          padding: const EdgeInsets.all(16),
                                          child: Text(
                                            l10n.scanning,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.black.withValues(alpha: .6),
                                            ),
                                          ),
                                        );
                                      case PrintersDiscoveryStatus.Success:
                                      case PrintersDiscoveryStatus.LoadingPrintTest:
                                      case PrintersDiscoveryStatus.LoadingPrintAdjust:
                                        final List<Printer> printers = statePrinters.printers ?? [];
                                        if (printers.isNotEmpty) {
                                          return _buildListPrinter(
                                            themePage,
                                            l10n,
                                            state,
                                            printers,
                                            state.name.value,
                                          );
                                        }
                                        return Container(
                                          width: MediaQuery.of(context).size.width,
                                          padding: const EdgeInsets.all(16),
                                          child: Text(
                                            l10n.noPrinter,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.black.withValues(alpha: .6),
                                            ),
                                          ),
                                        );

                                      default:
                                        return Container(
                                          width: MediaQuery.of(context).size.width,
                                          padding: const EdgeInsets.all(16),
                                          child: Text(
                                            l10n.noPrinter,
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.w400,
                                              color: Colors.black.withValues(alpha: .6),
                                            ),
                                          ),
                                        );
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            width: 1,
                            color: AppColors.appBorderColor,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                _buildTitle(themePage, l10n.ipAddress),
                                Transform.scale(
                                  scale: 0.7,
                                  child: CupertinoSwitch(
                                    value: state.isIPSetting,
                                    inactiveTrackColor: AppColors.appBGGreyColor,
                                    activeTrackColor: AppColors.cupertinoSwitchColor,
                                    onChanged: (value) {
                                      context.read<PrinterCubit>().onChangedIsIPSetting(value);
                                    },
                                  ),
                                ),
                              ],
                            ),
                            if (state.isIPSetting)
                              Column(
                                children: [
                                  Container(
                                    margin: const EdgeInsets.only(
                                      bottom: 24,
                                      top: 24,
                                    ),
                                    child: PrinterInputWidget(
                                      labelText: l10n.ipAddress,
                                      hintText: l10n.ipAddress,
                                      isValidate: state.isValidateIPAddress,
                                      initialValue: state.ipAddress.value,
                                      keyValue: state.ipAddress.value,
                                      keyboardType: const TextInputType.numberWithOptions(
                                        decimal: true,
                                        signed: true,
                                      ),
                                      inputFormatters: [
                                        MyInputFormatters.ipAddressInputFilter(),
                                        LengthLimitingTextInputFormatter(15),
                                        IpAddressInputFormatter(),
                                      ],
                                      errText: state.ipAddress.error == ValidateControlStatus.empty
                                          ? l10n.ipAddressRequired
                                          : state.ipAddress.error == ValidateControlStatus.invalid
                                              ? l10n.ipAddressIncorrect
                                              : '',
                                      onChanged: (String value) {
                                        context.read<PrinterCubit>().onChangedIPAddress(value);
                                      },
                                    ),
                                  ),
                                  PrinterInputWidget(
                                    labelText: l10n.port,
                                    hintText: l10n.port,
                                    initialValue: state.port?.value ?? '',
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                        RegExp(r'[0-9]'),
                                      ),
                                    ],
                                    onChanged: (value) {
                                      try {
                                        context.read<PrinterCubit>().onChangedPort(int.parse(value));
                                      } catch (e) {}
                                    },
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                      if (state.ipAddress.value.isNotEmpty)
                        BlocBuilder<PrintersDiscoveryCubit, PrintersDiscoveryState>(
                          builder: (context, printersDiscoveryState) {
                            final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
                            return Container(
                              alignment: Alignment.topRight,
                              margin: const EdgeInsets.only(top: 24),
                              child: ButtonLoading(
                                borderRadius: 0,
                                height: 48,
                                label: l10n.printTest,
                                fontSize: 16,
                                fontWeight: FontWeight.w700,
                                padding: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                  side: const BorderSide(
                                    color: AppColors.appTransparentColor,
                                  ),
                                ),
                                buttonBackgroundColor: AppColors.appBGAvatarColor,
                                buttonDisabledColor: AppColors.appTransparentColor,
                                circularStrokeColor: AppColors.darkPrimaryBackgroundColor,
                                labelColor: AppColors.darkPrimaryBackgroundColor,
                                isLoading: printersDiscoveryState.status == PrintersDiscoveryStatus.LoadingPrintTest ? true : false,
                                callback: () async {
                                  if (isReceipt) {
                                    final CustomerReceiptTemplateWidget customerPrint = CustomerReceiptTemplateWidget();
                                    final KitchenTicketTemplateWidget kitchenPrint = KitchenTicketTemplateWidget();
                                    await customerPrint.printCustomerReceiptAsText(
                                      context: context,
                                      ipAddress: state.ipAddress.value,
                                      order: Order(),
                                      isPrintTest: true,
                                    );
                                    // ignore: inference_failure_on_instance_creation
                                    await Future.delayed(const Duration(milliseconds: 500));
                                    await kitchenPrint.printKitchenTicketAsText(
                                      // context: context,
                                      l10n: l10n,
                                      timezone: timeZone,
                                      ipAddress: state.ipAddress.value,
                                      order: Order(),
                                      isPrintTest: true,
                                    );
                                  } else {
                                    await context
                                        .read<PrintersDiscoveryCubit>()
                                        .onPrinterTestReward(
                                          context,
                                          state.ipAddress.value,
                                          state.dpi.value,
                                        )
                                        .then((value) {
                                      if (!value) {
                                        AppBased.toastError(
                                          context,
                                          title: l10n.connectToPrinter,
                                        );
                                      }
                                    });
                                  }
                                  AppBased.doAuditLogging(
                                    pageName: 'PrinterAddPage',
                                    pageUrl: 'printer-add-new',
                                    pageAction: 'onPrinterTest',
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      if (state.ipAddress.value.isNotEmpty && !isReceipt)
                        BlocBuilder<PrintersDiscoveryCubit, PrintersDiscoveryState>(
                          builder: (context, printersDiscoveryState) {
                            return Container(
                              margin: const EdgeInsets.only(top: 16),
                              padding: const EdgeInsets.fromLTRB(0, 16, 16, 16),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColors.appBorderColor,
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  ButtonLoading(
                                    borderRadius: 0,
                                    height: 48,
                                    width: 120,
                                    label: l10n.adjust,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    padding: EdgeInsets.zero,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8.0),
                                      side: const BorderSide(
                                        color: AppColors.appTransparentColor,
                                      ),
                                    ),
                                    buttonBackgroundColor: AppColors.appTransparentColor,
                                    buttonDisabledColor: AppColors.appTransparentColor,
                                    circularStrokeColor: AppColors.darkPrimaryBackgroundColor,
                                    labelColor: AppColors.appResendColor,
                                    isLoading: printersDiscoveryState.status == PrintersDiscoveryStatus.LoadingPrintAdjust ? true : false,
                                    callback: () {
                                      context
                                          .read<PrintersDiscoveryCubit>()
                                          .onPrinterTestReward(
                                            context,
                                            state.ipAddress.value,
                                            state.dpi.value,
                                            isPrintTest: false,
                                          )
                                          .then((value) {
                                        if (!value) {
                                          AppBased.toastError(
                                            context,
                                            title: l10n.connectToPrinter,
                                          );
                                        }
                                      });
                                      AppBased.doAuditLogging(
                                        pageName: 'PrinterAddPage',
                                        pageUrl: 'printer-add-new',
                                        pageAction: 'onPrinterTestAdjust',
                                      );
                                    },
                                  ),
                                  Expanded(
                                    child: Text(
                                      l10n.printTestNote,
                                      style: TextStyle(
                                        color: AppColors.darkPrimaryBackgroundColor.withValues(alpha: .7),
                                        fontSize: 16,
                                        fontWeight: FontWeight.w400,
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      Container(
                        margin: const EdgeInsets.only(top: 48),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Expanded(
                              child: ButtonControlWidget(
                                height: 48,
                                padding: EdgeInsets.zero,
                                alignment: Alignment.center,
                                textCustom: Text(
                                  l10n.cancel,
                                  style: const TextStyle(
                                    color: Colors.transparent,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w400,
                                    // decoration: TextDecoration.underline,
                                    decorationColor: Colors.black,
                                    decorationThickness: 1.5,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(0, -2),
                                        color: Colors.black,
                                      ),
                                    ],
                                  ),
                                ),
                                buttonBackgroundColor: AppColors.lightPrimaryBackgroundColor,
                                buttonTextColor: Colors.black,
                                onPressed: () {
                                  FocusScope.of(context).unfocus();
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                            BlocConsumer<UpdatePrintersCubit, UpdatePrintersState>(
                              listener: (context, state) {
                                if (state.status == UpdatePrintersStatus.Success || state.status == UpdatePrintersStatus.Updated) {
                                  context.read<UpdatePrintersCubit>().onResetStatus();
                                  Navigator.pop(context, true);
                                } else if (state.status == UpdatePrintersStatus.Error) {
                                  AppBased.toastError(
                                    context,
                                    title: state.errorMsg,
                                  );
                                }
                              },
                              builder: (context, state) {
                                return Expanded(
                                  child: ButtonLoading(
                                    isLoading: state.status == UpdatePrintersStatus.Loading ? true : false,
                                    iconDirection: TextDirection.rtl,
                                    textDirection: TextDirection.rtl,
                                    height: 48,
                                    label: l10n.save,
                                    fontWeight: FontWeight.w600,
                                    buttonBackgroundColor: AppColors.appColor,
                                    labelColor: AppColors.lightPrimaryBackgroundColor,
                                    callback: () {
                                      FocusScope.of(context).unfocus();
                                      context.read<PrinterCubit>().doSavePrinter(
                                            isEdit: isEdit,
                                            index: index,
                                          );
                                    },
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(
    BuildContext context,
    String title,
    Printer? printer, {
    bool isEdit = false,
  }) {
    final l10n = context.l10n;
    return AppBar(
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      leading: Padding(
        padding: const EdgeInsets.all(10.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.lightPrimaryBackgroundColor,
            onPressed: () => Navigator.of(context).pop(),
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/arrow-back.svg',
            ),
          ),
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: AppColors.darkPrimaryBackgroundColor,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        IconButton(
          // padding: EdgeInsets.zero,
          splashRadius: 16,
          icon: SvgPicture.asset(
            'assets/svgs/help.svg',
            colorFilter: const ColorFilter.mode(
              AppColors.appBlackColor,
              BlendMode.srcIn,
            ),
          ),
          onPressed: () => AppBased.openShowModalBottomSheetOfferTag(
            context,
            paddingTop: 0,
            paddingRight: 0,
            paddingLeft: 0,
            widget: _buildSettingPrinter(context, l10n),
          ),
        ),
        if (isEdit == true)
          IconButton(
            // padding: EdgeInsets.zero,
            splashRadius: 16,
            icon: SvgPicture.asset(
              'assets/svgs/delete.svg',
              colorFilter: const ColorFilter.mode(
                AppColors.appBlackColor,
                BlendMode.srcIn,
              ),
            ),
            onPressed: () => AppBased.showDialogYesNo(
              context,
              title: l10n.confirm,
              noText: l10n.no,
              yesText: l10n.yes,
              msgContent: l10n.confirmDeleteMsg,
              noTap: () {},
              yesTap: () {
                Navigator.pop(context, printer);
              },
            ),
          ),
      ],
      centerTitle: true,
    );
  }

  Widget _buildSettingPrinter(BuildContext context, AppLocalizations l10n) {
    bool checkPermission = false;
    checkPermissionLocalNetwok().then((value) {
      checkPermission = value;
    });
    AppLog.e('checkPermission: $checkPermission');
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.only(bottom: 8),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(width: 1, color: AppColors.appBorderColor),
            ),
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () {},
                icon: const Icon(
                  Icons.close,
                  size: 18,
                  color: AppColors.appTransparentColor,
                ),
                splashRadius: 16,
              ),
              Expanded(
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12.0),
                    child: Text(
                      l10n.help,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
              IconButton(
                padding: EdgeInsets.zero,
                onPressed: () => Navigator.pop(context),
                icon: const Icon(
                  Icons.close,
                  size: 24,
                  color: AppColors.appImageIconColor,
                ),
                splashRadius: 16,
              ),
            ],
          ),
        ),
        Flexible(
          child: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.all(16),
              margin: const EdgeInsets.only(bottom: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.printerHelpStep1,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (Platform.isIOS) ...{
                    _buildLine(),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            l10n.printerHelpStep2,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: InkWell(
                            onTap: () => openAppSettings(),
                            child: Text(
                              l10n.setting,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: AppColors.appBlueColor,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  },
                  _buildLine(),
                  Column(
                    children: [
                      Text(
                        l10n.printerHelpStep3,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 6.0),
                        child: RichText(
                          textAlign: TextAlign.left,
                          text: TextSpan(
                            style: const TextStyle(
                              fontSize: 14,
                            ),
                            children: <InlineSpan>[
                              const TextSpan(
                                text: 'Zebra: ',
                                style: TextStyle(
                                  color: AppColors.appBlackColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              TextSpan(
                                text: l10n.printerHelpStep31,
                                style: const TextStyle(
                                  color: AppColors.appBlackColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 6.0),
                        child: RichText(
                          textAlign: TextAlign.left,
                          text: TextSpan(
                            style: const TextStyle(
                              fontSize: 16,
                            ),
                            children: <InlineSpan>[
                              const TextSpan(
                                text: 'Xprinter XP420B: ',
                                style: TextStyle(
                                  color: AppColors.appBlackColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              TextSpan(
                                text: l10n.printerHelpStep32,
                                style: const TextStyle(
                                  color: AppColors.appBlackColor,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  _buildLine(),
                  Text(
                    l10n.printerHelpStep4,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLine() {
    return Container(
      // padding: const EdgeInsets.symmetric(horizontal: 16),
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: const Divider(
        height: 1,
        color: AppColors.appBorderColor,
      ),
    );
  }

  Widget _buildTitle(AppThemePrinterPage themePage, String title) {
    return Container(
      alignment: Alignment.topLeft,
      child: Text(
        title,
        textAlign: TextAlign.left,
        style: TextStyle(
          color: themePage.textColor,
          fontSize: 16,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  Widget _buildListPrinter(
    AppThemePrinterPage themePage,
    AppLocalizations l10n,
    PrinterState state,
    List<Printer> printers,
    String? printerName,
  ) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: printers.length,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.all(16),
      itemBuilder: (context, index) {
        Printer printer = printers.elementAt(index);
        final double marginBototm = index == printers.length - 1 ? 0 : 16;
        if (printer.productName == null || printer.productName == '') {
          printer.productName = '${l10n.printer} ${index + 1}';
        }

        return Container(
          margin: EdgeInsets.only(bottom: marginBototm),
          decoration: BoxDecoration(
            color: AppColors.lightPrimaryBackgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(width: 1, color: AppColors.appBorderColor),
          ),
          child: ListTile(
            contentPadding: EdgeInsets.zero,
            dense: true,
            horizontalTitleGap: 0,
            title: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  printer.productName ?? '',
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    color: themePage.textColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Text(
                  printer.address ?? '',
                  textAlign: TextAlign.left,
                  style: TextStyle(
                    color: themePage.textColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            leading: Radio<Printer>(
              value: printer,
              groupValue: state.printer,
              activeColor: AppColors.darkPrimaryBackgroundColor,
              onChanged: (Printer? value) {
                if (value != null) {
                  printer = value;
                  context.read<PrinterCubit>().onChangedPrinter(printer);
                  context.read<PrinterCubit>().onChangedIPAddress(printer.address ?? '');
                  context.read<PrinterCubit>().onChangedPort(printer.jsonPortNumber ?? 9100);
                  if (printerName == null || printerName == '') {
                    context.read<PrinterCubit>().onChangedName(printer.productName ?? '');
                  }
                }
              },
            ),
            onTap: () {
              context.read<PrinterCubit>().onChangedPrinter(printer);
              context.read<PrinterCubit>().onChangedIPAddress(printer.address ?? '');
              context.read<PrinterCubit>().onChangedPort(printer.jsonPortNumber ?? 9100);
              if (printerName == null || printerName == '') {
                context.read<PrinterCubit>().onChangedName(printer.productName ?? '');
              }
            },
          ),
        );
      },
    );
  }

  Widget dropDownDPI(
    BuildContext context,
    PrinterState state,
    String title, {
    List<String>? list,
    String? value,
    String? hint,
    ValueChanged<String>? onChanged,
  }) {
    final String? valueData = value != '' ? value : null;

    return Theme(
      data: Theme.of(context).copyWith(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
      ),
      child: SizedBox(
        height: 64,
        child: Builder(
          builder: (context) {
            final width = MediaQuery.of(context).size.width;
            return PopupMenuButton<String>(
              offset: Offset(0, AppBar().preferredSize.height),
              onSelected: (valueChange) {
                onChanged?.call(valueChange);
              },
              constraints: BoxConstraints(
                minWidth: width - 32,
                maxWidth: width - 32,
              ),
              child: Container(
                padding: const EdgeInsets.fromLTRB(8, 8, 10, 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    width: 1,
                    color: AppColors.appBorderColor,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Text(
                                title,
                                style: TextStyle(
                                  color: AppColors.appBlackColor.withValues(alpha: .8),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Text(
                              valueData ?? hint ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-down-icon.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 16,
                        height: 16,
                      ),
                    )
                  ],
                ),
              ),
              itemBuilder: (_) => (list ?? []).map((String value) {
                return PopupMenuItem(
                  value: value,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        value,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (value == valueData)
                        Padding(
                          padding: const EdgeInsets.only(left: 6.0),
                          child: SvgPicture.asset(
                            'assets/svgs/check-calendar.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.darkPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        )
                    ],
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Widget dropDownPageSize(
    BuildContext context,
    PrinterState state,
    String title, {
    List<String>? list,
    String? value,
    String? hint,
    ValueChanged<String>? onChanged,
  }) {
    final String? valueData = value != '' ? value : null;

    return Theme(
      data: Theme.of(context).copyWith(
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
      ),
      child: SizedBox(
        height: 64,
        child: Builder(
          builder: (context) {
            final width = MediaQuery.of(context).size.width;
            return PopupMenuButton<String>(
              offset: Offset(0, AppBar().preferredSize.height),
              onSelected: (valueChange) {
                onChanged?.call(valueChange);
              },
              constraints: BoxConstraints(
                minWidth: width - 32,
                maxWidth: width - 32,
              ),
              child: Container(
                padding: const EdgeInsets.fromLTRB(8, 8, 10, 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    width: 1,
                    color: AppColors.appBorderColor,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Text(
                                title,
                                style: TextStyle(
                                  color: AppColors.appBlackColor.withValues(alpha: .8),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Text(
                              valueData ?? hint ?? '',
                              style: const TextStyle(
                                fontSize: 16,
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 2),
                      child: SvgPicture.asset(
                        'assets/svgs/arrow-down-icon.svg',
                        colorFilter: const ColorFilter.mode(
                          AppColors.darkPrimaryBackgroundColor,
                          BlendMode.srcIn,
                        ),
                        width: 16,
                        height: 16,
                      ),
                    )
                  ],
                ),
              ),
              itemBuilder: (_) => (list ?? []).map((String value) {
                return PopupMenuItem(
                  value: value,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        value,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.4,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (value == valueData)
                        Padding(
                          padding: const EdgeInsets.only(left: 6.0),
                          child: SvgPicture.asset(
                            'assets/svgs/check-calendar.svg',
                            colorFilter: const ColorFilter.mode(
                              AppColors.darkPrimaryBackgroundColor,
                              BlendMode.srcIn,
                            ),
                          ),
                        )
                    ],
                  ),
                );
              }).toList(),
            );
          },
        ),
      ),
    );
  }

  Future<bool> checkPermissionLocalNetwok() async {
    final result = await FlutterLocalNetworkIos().requestAuthorization() ?? false;
    return result;
  }
}
