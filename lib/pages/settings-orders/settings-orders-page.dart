// ignore_for_file: strict_raw_type

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@const/routes.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/business-profile/business-profile.cubit.dart';
import 'package:stickyqrbusiness/@core/store/change-plan-features-info-content/change-plan-features-info-content.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-auto-complete-store-hours/setting-auto-complete-store-hours.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-order-auto-completed/setting-order-auto-completed.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-order-auto-confirm/setting-order-auto-confirm.cubit.dart';
import 'package:stickyqrbusiness/@core/store/setting-order-auto-ready/setting-order-auto-ready.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/announcements/widgets/date-time/ann-timepicker-widget.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings-new/widgets/@setting-widget.dart';
import 'package:stickyqrbusiness/pages/settings/widgets/settings-item-widget.dart';

class SettingsOrdersPage extends StatelessWidget {
  const SettingsOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final Business? business = context.read<AuthBloc>().state.business;
    final timeZone = context.read<AuthBloc>().state.businessTimeZone ?? '';

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (value, f) {
        return;
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => SettingOrderAufoConfirmCubit()..onChangeToggle(business?.orderAutoConfirm ?? false),
          ),
          BlocProvider(
            create: (context) => SettingAutoCompleteStoreHoursCubit()..getRegularAndSpecialHours(),
          ),
          BlocProvider(
            create: (context) => SettingOrderAutoReadyCubit()..onChangeToggle(business?.orderAutoReady ?? false),
          ),
          BlocProvider(
            create: (context) => SettingOrderAutoCompletedCubit()
              ..onChangeToggle(business?.orderAutoComplete ?? false)
              ..onChangeTimeLocal(null)
              ..onChangeTime(business?.orderAutoCompleteAfter != null ? DateTime.parse(formatUtcToLocal(DateTimeHelper.formatDateTime(parseHHMMToToday(business?.orderAutoCompleteAfter), timeZone).toString())) : null),
          ),
        ],
        child: const SettingsOrdersView(),
      ),
    );
  }

  String formatUtcToLocal(String utcTimeString) {
    try {
      DateTime utcDateTime;

      try {
        utcDateTime = DateTime.parse('${utcTimeString}Z');
      } catch (e) {
        try {
          if (utcTimeString.endsWith('Z') || utcTimeString.contains('+00:00')) {
            utcDateTime = DateTime.parse(utcTimeString);
          } else {
            utcDateTime = DateTime.parse(utcTimeString).toUtc();
          }
        } catch (e2) {
          final RegExp pattern = RegExp(r'(\d{4})-(\d{2})-(\d{2})\s+(\d{2}):(\d{2}):(\d{2})(?:\.(\d{3}))?');
          final match = pattern.firstMatch(utcTimeString);

          if (match != null) {
            final year = int.parse(match.group(1)!);
            final month = int.parse(match.group(2)!);
            final day = int.parse(match.group(3)!);
            final hour = int.parse(match.group(4)!);
            final minute = int.parse(match.group(5)!);
            final second = int.parse(match.group(6)!);
            final millisecond = match.group(7) != null ? int.parse(match.group(7)!) : 0;

            utcDateTime = DateTime.utc(year, month, day, hour, minute, second, millisecond);
          } else {
            throw Exception('Không thể phân tích chuỗi thời gian: $utcTimeString');
          }
        }
      }

      // Chuyển sang múi giờ địa phương
      final localDateTime = utcDateTime.toLocal();

      return '${localDateTime.year}-${_padZero(localDateTime.month)}-${_padZero(localDateTime.day)} '
          '${_padZero(localDateTime.hour)}:${_padZero(localDateTime.minute)}:${_padZero(localDateTime.second)}';
    } catch (e) {
      return utcTimeString;
    }
  }

  String _padZero(int number) {
    return number.toString().padLeft(2, '0');
  }

  DateTime parseHHMMToToday(dynamic hhmmInput) {
    String hhmm;
    if (hhmmInput is int) {
      if (hhmmInput < 0) {
        throw const FormatException('Input must be a positive integer');
      }
      hhmm = hhmmInput.toString().padLeft(4, '0');
    } else if (hhmmInput is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(hhmmInput)) {
        throw const FormatException('String must be 1 to 4 digits in HHMM format');
      }
      hhmm = hhmmInput.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0-23), minute (0-59)');
    }

    final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59);
    return DateTime(now.year, now.month, now.day, hour, minute);
  }
}

class SettingsOrdersView extends StatefulWidget {
  const SettingsOrdersView({super.key});

  @override
  State<SettingsOrdersView> createState() => _SettingsOrdersViewState();
}

class _SettingsOrdersViewState extends State<SettingsOrdersView> {
  late final l10n = context.l10n;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    return Scaffold(
      key: _scaffoldKey,
      appBar: _buildAppbar(context),
      endDrawer: const OrdersActiveDrawerWidget(activePage: OrderDrawerPageActive.Settings),
      body: SafeArea(
        bottom: false,
        child: _buildBody(context, l10n),
      ),
    );
  }

  AppBar _buildAppbar(BuildContext context) {
    return AppBar(
      elevation: 1,
      scrolledUnderElevation: 1,
      shadowColor: Colors.grey.withValues(alpha: .2),
      automaticallyImplyLeading: false,
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      centerTitle: true,
      title: Text(
        l10n.settingsOrders,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 8.0),
          child: IconButton(
            splashRadius: 16,
            style: IconButton.styleFrom(
              backgroundColor: AppColors.appTransparentColor,
            ),
            icon: SvgPicture.asset(
              'assets/svgs/menu.svg',
              colorFilter: const ColorFilter.mode(
                AppColors.appBlackColor,
                BlendMode.srcIn,
              ),
              width: 32,
              height: 32,
            ),
            onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
          ),
        )
      ],
    );
  }

  Widget _buildBody(BuildContext context, AppLocalizations l10n) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, stateAuth) {
        final timeZone = stateAuth.business?.timeZone ?? '';
        final isPlanProEnterprise = stateAuth.isPro;
        final checkPro = isPlanProEnterprise == true ? false : true;
        // final checkPro = isPlanProEnterprise ? false : feature?.isProRequired == true;
        final is24H = Localizations.localeOf(context).languageCode == 'vi' ? true : false;
        return ListView(
          physics: const AlwaysScrollableScrollPhysics(
            parent: ClampingScrollPhysics(),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16),
          children: [
            _buildItem(
              icon: 'auto-confirm',
              title: l10n.autoConfirmNewOrder,
              subTitle: l10n.serviceSubtitle,
              func: () {},
              // isTrailing: true,
              trailingWidget: _buildEnableAutoConfirmBtn(),
            ),
            _buildItem(
              icon: 'auto-ready',
              title: l10n.autoReadyOrder,
              subTitle: '',
              func: () {},
              // isTrailing: true,
              trailingWidget: _buildEnableAutoReadyBtn(),
            ),
            _buildItem(
              icon: 'auto-ready',
              title: l10n.autoCompleteOrder,
              subTitle: '',
              func: () {},
              // isTrailing: true,
              trailingWidget: _buildEnableAutoCompletedBtn(timeZone),
              subWidget: BlocBuilder<SettingOrderAutoCompletedCubit, SettingOrderAutoCompletedState>(
                builder: (context, state) {
                  final isEnable = state.enableAutoComplete ?? false;
                  if (isEnable != true) return const SizedBox.shrink();

                  final DateTime dateTime = state.orderAutoCompleteAfterLocal ?? (state.orderAutoCompleteAfter ?? DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59));
                  final time = formatHHMMFromDateTime(dateTime);
                  AppLog.e('timetimetimetimetime: $time');

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Wrap(
                        direction: Axis.horizontal,
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 8.0),
                            child: Text(
                              l10n.automaticallyClearsAllReadyOrdersAt,
                              style: const TextStyle(
                                fontSize: 14,
                                color: AppColors.appBlackColor,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () => _openTimePicker(
                              context,
                              dateTime,
                              l10n,
                              onChange: (time) {
                                if (time == null) return;
                                AppLog.e('Time: $time');
                                final dateTimeParse = DateTime.parse(time.toString());

                                final formatTime = DateTimeHelper.convertToUtc(
                                  dateTimeParse,
                                  timeZone,
                                );
                                final parseHHMMFormat = ((formatTime?.hour ?? 0) * 100) + (formatTime?.minute ?? 0);

                                final body = {
                                  'orderAutoComplete': isEnable,
                                  'orderAutoCompleteAfter': parseHHMMFormat,
                                };
                                context.read<SettingOrderAutoCompletedCubit>()
                                  ..onChangeTimeLocal(dateTimeParse)
                                  ..onUpdateAutoCompletedOrder(body);
                              },
                            ),
                            child: Container(
                              width: 130,
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  width: 1,
                                  color: AppColors.appBorderColor,
                                ),
                              ),
                              alignment: Alignment.center,
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Container(
                                      padding: const EdgeInsets.only(right: 16),
                                      alignment: Alignment.center,
                                      child: Text(
                                        formatHHMM(time, is24H: is24H),
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: AppColors.appBlackColor,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ),
                                  SvgPicture.asset(
                                    'assets/svgs/engagement-arrow-down.svg',
                                    colorFilter: const ColorFilter.mode(
                                      AppColors.appBlackColor,
                                      BlendMode.srcIn,
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 12.0),
                        child: RichText(
                          textAlign: TextAlign.left,
                          text: TextSpan(
                            children: <InlineSpan>[
                              TextSpan(
                                text: l10n.tipNote,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.homeShowQRBGColor,
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              TextSpan(
                                text: l10n.automaticallyClearsNote2,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: AppColors.homeShowQRBGColor,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      BlocBuilder<SettingAutoCompleteStoreHoursCubit, SettingAutoCompleteStoreHoursState>(
                        builder: (context, state) {
                          final checkTime = state.checkTimeAutoComplete(dateTime, timeZone);
                          AppLog.e('checkTime: $checkTime');
                          if (!checkTime) {
                            return const SizedBox.shrink();
                          }
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: AppColors.startDateTimeBGColor.withValues(alpha: .12),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SvgPicture.asset(
                                    'assets/svgs/warning-delete.svg',
                                    width: 24,
                                    height: 24,
                                    colorFilter: const ColorFilter.mode(
                                      AppColors.orderSettingAutoCompleted,
                                      BlendMode.srcIn,
                                    ),
                                  ),
                                  Flexible(
                                    child: Padding(
                                      padding: const EdgeInsets.only(left: 8.0),
                                      child: Text(
                                        l10n.automaticallyClearsNote1,
                                        style: const TextStyle(
                                          fontSize: 12,
                                          color: AppColors.homeShowQRBGColor,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
            _buildItem(
              icon: 'clock',
              title: l10n.storeHours,
              subTitle: l10n.serviceSubtitle,
              func: () => AppBased.go(context, AppRoutes.settingsOrdersStoreHours),
            ),
            _buildItem(
              icon: 'store-status',
              title: l10n.updateStoreStatus,
              subTitle: l10n.serviceSubtitle,
              func: () => AppBased.go(context, AppRoutes.updateStoreStatus),
            ),
            _buildItem(
              icon: 'alert',
              title: l10n.orderAlert,
              subTitle: l10n.tableSubtitle,
              func: () => AppBased.go(context, AppRoutes.settingsOrdersAlert),
            ),
            _buildItem(
              icon: 'merge',
              title: l10n.integrations,
              subTitle: '',
              checkPro: checkPro,
              func: checkPro
                  ? () => _diaLogProPlan(context, l10n, 'ORDERS_INTEGRATIONS')
                  : () {
                      AppBased.go(context, AppRoutes.integrationsPage);
                    },
            ),
            _buildItem(
              icon: 'print',
              title: l10n.orderReceiptPrinters,
              subTitle: l10n.tableSubtitle,
              func: () => AppBased.go(context, AppRoutes.settingsOrdersPrinter),
              // title: 'Hardware Printer',
              // subTitle: l10n.tableSubtitle,
              // func: () => AppBased.go(context, AppRoutes.hardwarePrinterpage),
            ),
            _buildItem(
              icon: 'print',
              title: l10n.hardware,
              subTitle: '', // l10n.tableSubtitle,
              func: () => AppBased.go(
                context,
                AppRoutes.hardwarePage,
              ),
            ),
          ],
        );
      },
    );
  }

  int formatHHMMFromDateTime(DateTime formatTime) {
    return (formatTime.hour * 100) + formatTime.minute;
  }

  DateTime parseHHMMToToday(dynamic hhmmInput) {
    String hhmm;
    if (hhmmInput is int) {
      if (hhmmInput < 0) {
        throw const FormatException('Input must be a positive integer');
      }
      hhmm = hhmmInput.toString().padLeft(4, '0');
    } else if (hhmmInput is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(hhmmInput)) {
        throw const FormatException('String must be 1 to 4 digits in HHMM format');
      }
      hhmm = hhmmInput.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }
    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));
    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0-23), minute (0-59)');
    }
    final DateTime now = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59);
    return DateTime(now.year, now.month, now.day, hour, minute);
  }

  String formatHHMM(dynamic input, {bool is24H = true}) {
    String hhmm;
    if (input is int) {
      if (input < 0) throw const FormatException('Negative number not allowed');
      hhmm = input.toString().padLeft(4, '0');
    } else if (input is String) {
      if (!RegExp(r'^\d{1,4}$').hasMatch(input)) {
        throw const FormatException('Input string must be 1 to 4 digits (HHMM)');
      }
      hhmm = input.padLeft(4, '0');
    } else {
      throw const FormatException('Input must be String or int');
    }

    final int hour = int.parse(hhmm.substring(0, 2));
    final int minute = int.parse(hhmm.substring(2, 4));

    if (hour < 0 || hour > 23 || minute < 0 || minute > 59) {
      throw const FormatException('Invalid time: hour (0–23), minute (0–59)');
    }

    if (is24H) {
      final String h = hour.toString().padLeft(2, '0');
      final String m = minute.toString().padLeft(2, '0');
      return '$h:$m';
    } else {
      final isPM = hour >= 12;
      final hour12 = hour % 12 == 0 ? 12 : hour % 12;
      final m = minute.toString().padLeft(2, '0');
      final suffix = isPM ? 'PM' : 'AM';
      return '$hour12:$m $suffix';
    }
  }

  void _diaLogProPlan(BuildContext context, AppLocalizations l10n, String key) {
    final bid = context.read<AuthBloc>().state.bid;
    context.read<ChangePlanFeaturesInfoCubit>().doGetPlanFeaturesInfo(bid: bid, key: key);
    AppBased.openPROButtomSheetDraggableScrollable(
      context,
      titleFeature: l10n.labelTemplate,
      widget: UpgradeProContentButtomSheetWidget(type: key),
      widgetButtom: const UpgradeProIndexSliderWidget(),
      onTap: () {
        Navigator.pop(context);
      },
    );
  }

  Widget _buildEnableAutoConfirmBtn() {
    return BlocConsumer<SettingOrderAufoConfirmCubit, SettingOrderAufoConfirmState>(
      listener: (context, state) {
        final stt = state.status;
        AppBased.loading(isShow: stt == SettingOrderAufoConfirmStatus.Loading);
        switch (stt) {
          case SettingOrderAufoConfirmStatus.Success:
            // context.read<BusinessProfileCubit>().getBusinessProfile();
            break;
          case SettingOrderAufoConfirmStatus.Error:
            if (state.errorMsg?.isNotEmpty == true) {
              AppBased.toastError(context, title: state.errorMsg);
            }
            break;
          default:
            break;
        }
      },
      builder: (context, state) {
        final isEnable = state.enableAutoConfirm ?? false;
        return Transform.scale(
          scale: 0.85,
          child: SizedBox(
            height: 30,
            child: CupertinoSwitch(
              value: isEnable,
              inactiveTrackColor: AppColors.appBGGreyColor,
              activeTrackColor: AppColors.cupertinoSwitchColor,
              onChanged: (value) {
                final body = {
                  'orderAutoConfirm': value,
                };
                context.read<SettingOrderAufoConfirmCubit>()
                  ..onChangeToggle(value)
                  ..onUpdateAutoConfirmOrder(body);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnableAutoReadyBtn() {
    return BlocConsumer<SettingOrderAutoReadyCubit, SettingOrderAutoReadyState>(
      listener: (context, state) {
        final stt = state.status;
        AppBased.loading(isShow: stt == SettingOrderAutoReadyStatus.Loading);
        switch (stt) {
          case SettingOrderAutoReadyStatus.Success:
            // context.read<BusinessProfileCubit>().getBusinessProfile();
            break;
          case SettingOrderAutoReadyStatus.Error:
            if (state.errorMsg?.isNotEmpty == true) {
              AppBased.toastError(context, title: state.errorMsg);
            }
            break;
          default:
            break;
        }
      },
      builder: (context, state) {
        final isEnable = state.enableAutoReady ?? false;
        return Transform.scale(
          scale: 0.85,
          child: SizedBox(
            height: 30,
            child: CupertinoSwitch(
              value: isEnable,
              inactiveTrackColor: AppColors.appBGGreyColor,
              activeTrackColor: AppColors.cupertinoSwitchColor,
              onChanged: (value) {
                final body = {
                  'orderAutoReady': value,
                };
                context.read<SettingOrderAutoReadyCubit>()
                  ..onChangeToggle(value)
                  ..onUpdateAutoReadyOrder(body);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnableAutoCompletedBtn(String timeZone) {
    return BlocConsumer<SettingOrderAutoCompletedCubit, SettingOrderAutoCompletedState>(
      listener: (context, state) {
        final stt = state.status;
        AppBased.loading(isShow: stt == SettingOrderAutoCompletedStatus.Loading);
        switch (stt) {
          case SettingOrderAutoCompletedStatus.Success:
            // context.read<BusinessProfileCubit>().getBusinessProfile();
            break;
          case SettingOrderAutoCompletedStatus.Error:
            if (state.errorMsg?.isNotEmpty == true) {
              AppBased.toastError(context, title: state.errorMsg);
            }
            break;
          default:
            break;
        }
      },
      builder: (context, state) {
        final isEnable = state.enableAutoComplete ?? false;
        final time = state.orderAutoCompleteAfter ?? DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day, 23, 59);
        final formatTime = DateTimeHelper.convertToUtc(
          time,
          timeZone,
        );
        final parseHHMMFormat = ((formatTime?.hour ?? 0) * 100) + (formatTime?.minute ?? 0);
        return Transform.scale(
          scale: 0.85,
          child: SizedBox(
            height: 30,
            child: CupertinoSwitch(
              value: isEnable,
              inactiveTrackColor: AppColors.appBGGreyColor,
              activeTrackColor: AppColors.cupertinoSwitchColor,
              onChanged: (value) {
                final body = {
                  'orderAutoComplete': value,
                  'orderAutoCompleteAfter': parseHHMMFormat,
                };
                context.read<SettingOrderAutoCompletedCubit>()
                  ..onChangeToggle(value)
                  ..onUpdateAutoCompletedOrder(body);
              },
            ),
          ),
        );
      },
    );
  }

  void _openTimePicker(
    BuildContext context,
    DateTime date,
    AppLocalizations l10n, {
    ValueChanged? onChange,
  }) {
    final bool islanguageVN = Localizations.localeOf(context).languageCode == 'vi';
    AppBased.openShowModalBottomSheetSetHight(
      context,
      title: l10n.timePicker,
      widget: AnnTimepickerWidget(
        dateTime: date,
        is24h: islanguageVN,
        onChange: (DateTime? date) {
          AppLog.e('Time: $date');
          onChange?.call(date);
        },
      ),
    );
  }

  Widget _buildItem({
    required String icon,
    required String title,
    required String subTitle,
    required Function func,
    Widget? subWidget,
    bool isBorder = true,
    Widget? trailingWidget,
    bool isTrailing = false,
    bool checkPro = false,
  }) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: InkWell(
            onTap: isTrailing
                ? null
                : () {
                    func.call();
                  },
            child: Container(
              decoration: BoxDecoration(
                border: isBorder
                    ? const Border(
                        bottom: BorderSide(
                          width: 1,
                          color: Color(0xFFEBEBEB),
                        ),
                      )
                    : null,
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Opacity(
                opacity: checkPro == true ? .4 : 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: subWidget == null ? CrossAxisAlignment.center : CrossAxisAlignment.start,
                      children: [
                        Stack(
                          children: [
                            SvgPicture.asset(
                              'assets/svgs/$icon.svg',
                              width: 24,
                              height: 24,
                            ),
                            if (title == l10n.updateStoreStatus)
                              Positioned(
                                bottom: 0,
                                right: 1,
                                child: BlocBuilder<BusinessProfileCubit, BusinessProfileState>(
                                  builder: (context, state) {
                                    final isAllowOnlineOrdering = state.businessProfile.orderAllowOnlineOrdering ?? false;
                                    final storeStatus = state.businessProfile.orderOperationStatus;
                                    final color = !isAllowOnlineOrdering
                                        ? Colors.grey
                                        : storeStatus == 'ACTIVE'
                                            ? Colors.green
                                            : storeStatus == 'PAUSED'
                                                ? Colors.red
                                                : Colors.orange;
                                    return Container(
                                      width: 9,
                                      height: 9,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: color,
                                      ),
                                    );
                                  },
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Text(
                            title,
                            style: const TextStyle(
                              fontSize: 18,
                            ),
                          ),
                        ),
                        trailingWidget ?? const SizedBox.shrink(),
                        if (checkPro == true) ...{
                          const ProSettingWidget(),
                        }
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.only(left: 40, right: 32),
                      child: subWidget ?? const SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
