import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@core/store/customize-kitchen-ticket/customize-kitchen-ticket.cubit.dart';
import 'package:stickyqrbusiness/@utils/datetime.dart';
import 'package:stickyqrbusiness/@widgets/custom-loading.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/capture-receipt-widget.dart';

class CustomizeKitchenTicketPreviewWidget extends StatefulWidget {
  final bool isShowAppbar;
  const CustomizeKitchenTicketPreviewWidget({
    super.key,
    this.isShowAppbar = false,
  });

  @override
  State<CustomizeKitchenTicketPreviewWidget> createState() =>
      _CustomizeKitchenTicketPreviewWidgetState();
}

class _CustomizeKitchenTicketPreviewWidgetState
    extends State<CustomizeKitchenTicketPreviewWidget>
    with WidgetsBindingObserver {
  late final l10n = context.l10n;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      left: false,
      right: false,
      bottom: false,
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        appBar: widget.isShowAppbar ? _buildAppbar() : null,
        body: BlocBuilder<CustomizeKitchenTicketCubit, CustomizeKitchenTicketState>(
          builder: (context, state) {
            final status = state.status;
            if (status == CustomizeKitchenTicketStatus.Loading) {
              return _buildLoading();
            }
            return ListView(
              children: [
                _buildSelectFontSize(),
              ],
            );
          },
        ),
      ),
    );
  }

  AppBar _buildAppbar() {
    return AppBar(
      elevation: 1,
      scrolledUnderElevation: 1,
      shadowColor: Colors.grey.withValues(alpha: .2),
      automaticallyImplyLeading: false,
      centerTitle: true,
      title: Text(
        l10n.preview,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 6),
          child: IconButton(
            icon: const Icon(
              Icons.close,
              size: 26,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectFontSize() {
    return BlocBuilder<CustomizeKitchenTicketCubit, CustomizeKitchenTicketState>(
      builder: (context, state) {
        final fontSize = state.fontSize;
        final fontSizeTitle = _getFontSizeTitleName(fontSize ?? 1);
        final fontSizeModifier = _getFontSizeModifierName(fontSize ?? 1);

        final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
        final DateTime now = DateTimeHelper.currentTime(timeZone);
        final String formattedDate = intl.DateFormat('d MMM, y').format(now);
        final String formattedTime = intl.DateFormat('hh:mm a').format(now);

        return Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
               const Text(
                '# 001',
                style: TextStyle(fontSize: 28, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Center(
                child: SizedBox(
                  width: double.infinity,
                  child: CustomPaint(
                    painter: DashedLinePainter(),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              const Center(
                child: Text(
                  'PICK UP',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Center(
                child: SizedBox(
                  width: double.infinity,
                  child: CustomPaint(
                    painter: DashedLinePainter(),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              const Center(
                child: Text(
                  'Customer Name',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    // fontFamily: 'Homenaje',
                  ),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Server: ${l10n.kitchenStaff}',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                ),
              ),
              Text(
                '$formattedDate, $formattedTime',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.normal,
                ),
              ),

              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Divider(thickness: 1, color: Colors.black),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '2',
                    style: TextStyle(
                      fontSize: fontSizeTitle,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '51. Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli',
                          style: TextStyle(
                            fontSize: fontSizeTitle,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Chow Mein\nPork',
                          style: TextStyle(
                            fontSize: fontSizeModifier,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Divider(thickness: 1, color: Colors.black),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1',
                    style: TextStyle(
                      fontSize: fontSizeTitle,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Salad Mix with Grilled Pork/Chicken',
                          style: TextStyle(
                            fontSize: fontSizeTitle,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Egg',
                          style: TextStyle(
                            fontSize: fontSizeModifier,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1',
                    style: TextStyle(
                      fontSize: fontSizeTitle,
                      fontWeight: FontWeight.bold,
                      color: Colors.transparent,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'Note',
                    style: TextStyle(
                      fontSize: fontSizeModifier,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1',
                    style: TextStyle(
                      fontSize: fontSizeTitle,
                      fontWeight: FontWeight.bold,
                      color: Colors.transparent,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    'Example ...',
                    style: TextStyle(
                      fontSize: fontSizeModifier,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.only(top: 10, bottom: 5),
                child: Divider(thickness: 1, color: Colors.black),
              ),
              const Center(
                child: Text(
                  'Total Items: 3',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    final width = (MediaQuery.of(context).size.width) * 0.6 - 70;
    final item = Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ItemLoadingCustomWidget(width: width * .6, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
        const SizedBox(height: 48),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
      ],
    );
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 24),
      children: <Widget>[
        LoadingCustomWidget(
          lineItem: 1,
          isBorder: false,
          isPadding: true,
          paddingList: EdgeInsets.zero,
          paddingItem: const EdgeInsets.only(bottom: 8),
          widget: item,
        ),
      ],
    );
  }

  String _getFontSizeName(int fontSize) {
    switch (fontSize) {
      case 0:
        return l10n.kitchenTicketSmall;
      case 1:
        return l10n.kitchenTicketDefault;
      case 2:
        return l10n.kitchenTicketLarge;
      default:
        return l10n.kitchenTicketDefault;
    }
  }

  double _getFontSizeTitleName(int fontSize) {
    switch (fontSize) {
      case 0:
        return 20;
      case 1:
        return 24;
      case 2:
        return 28;
      default:
        return 24;
    }
  }

  double _getFontSizeModifierName(int fontSize) {
    switch (fontSize) {
      case 0:
        return 16;
      case 1:
        return 20;
      case 2:
        return 24;
      default:
        return 20;
    }
  }

  void _buildCompleted() {}
}

class FontSize {
  FontSize({
    required this.title,
    required this.value,
  });
  final String title;
  final String value;
}
