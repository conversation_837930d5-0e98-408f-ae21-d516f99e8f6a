import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:diacritic/diacritic.dart';
import 'package:esc_pos_printer_lts/esc_pos_printer_lts.dart';
import 'package:esc_pos_utils_lts/esc_pos_utils_lts.dart';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import 'package:stickyqrbusiness/@common/_local-storage.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/models/ordering-modifier.model.dart';
import 'package:stickyqrbusiness/@core/models/receipt-template.model.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/order-define.dart';

class KitchenTicketTemplateWidget {

  PosTextSize _getProductNameSize(int fontSize) {
    switch (fontSize) {
      case 0:
        return PosTextSize.size1;
      case 1:
        return PosTextSize.size2;
      case 2:
        return PosTextSize.size3;
      default:
        return PosTextSize.size2;
    }
  }

  PosTextSize _getModifierSize(int fontSize) {
    switch (fontSize) {
      case 0:
        return PosTextSize.size1;
      case 1:
        return PosTextSize.size1;
      case 2:
        return PosTextSize.size2;
      default:
        return PosTextSize.size1;
    }
  }

  // Helper để debug font size
  String _fontSizeToString(PosTextSize fontSize) {
    switch (fontSize) {
      case PosTextSize.size1:
        return 'PosTextSize.size1';
      case PosTextSize.size2:
        return 'PosTextSize.size2';
      case PosTextSize.size3:
        return 'PosTextSize.size3';
      case PosTextSize.size4:
        return 'PosTextSize.size4';
      default:
        return 'Unknown';
    }
  }

  String _sanitizeText(String? text) {
    if (text == null || text.isEmpty) return '';

    // Loại bỏ các ký tự có thể gây vấn đề
    // String sanitized = text
    //     .replaceAll(RegExp(r'[^\x20-\x7E\u00A0-\u00FF]'), '') // Chỉ giữ ASCII và Latin-1
    //     .replaceAll(RegExp(r'\s+'), ' ') // Normalize whitespace
    //     .trim();

    // return removeDiacritics(sanitized);
    return removeDiacritics(text);
  }

  final Order orderTest = Order(
    orderNumber: '123',
    customerName: 'Customer Name',
    items: [
      ItemOrder(
        title: 'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli',
        subtotal: 71,
        quantity: 1,
        total: 71,
        unitPrice: 71,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
      ItemOrder(
        title: 'Salad Mix with Grilled Pork/Chicken',
        subtotal: 25,
        quantity: 1,
        total: 25,
        unitPrice: 25,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
    ],
  );
  
  Future<void> printKitchenTicketAsText({
    // required BuildContext context,
    required AppLocalizations l10n,
    required String timezone,
    required String ipAddress,
    required Order order,
    String? logoPath,
    String? printerType,
    bool isPrintTest = false,
  }) async {
    print('printKitchenTicketAsText');
    final Order orderPrint = isPrintTest ? orderTest : order;
    final List<ItemOrder> receiptItems = orderPrint.items ?? [];
    final String? language = await AppLocalStorage.getLanguage() ?? Platform.localeName;

    // final DateTime now = DateTimeHelper.currentTime(timezone);
    final orderTime = orderPrint.createdAt ?? orderPrint.updatedAt ?? DateTimeHelper.currentTime(timezone);
    final orderTimeFormat = DateTimeHelper.dateTimeLongFormat(
        orderTime,
        timeZone: timezone,
        isWeekDay: false,
        langLocale: language,
        format: 'd MMM, y - hh:mm a',
      );
    // final String formattedDate = intl.DateFormat('d MMM, y').format(orderTime);
    // final String formattedTime = intl.DateFormat('hh:mm a').format(orderTime);
    final customerName = orderPrint.customerName ?? orderPrint.customer?.displayName ?? '';
    // final type = orderPrint.type ?? '';
    final orderType = order.type?.toUpperCase() == OrderType.DELIVERY.name ? l10n.delivery : l10n.receiptPickup;

    try {
      final ReceiptTemplate? template =await AppLocalStorage.getKitchenReceipt();
      final int fontSize = template?.kitchenReceiptFontSize ?? 0;

      final PosTextSize fontSizeProductName = isPrintTest ? PosTextSize.size1 : _getProductNameSize(fontSize);
      final PosTextSize fontSizeModifier = isPrintTest ? PosTextSize.size1 : _getModifierSize(fontSize);

      print('fontSize setting: $fontSize');
      print('fontSizeProductName: ${_fontSizeToString(fontSizeProductName)}');
      print('fontSizeModifier: ${_fontSizeToString(fontSizeModifier)}');

      final profile = await CapabilityProfile.load();
      final printer = NetworkPrinter(PaperSize.mm80, profile);

      final connectResult = await printer.connect(ipAddress, port: 9100);
      AppLog.e('connectResult = ${connectResult.value} // check succss = ${connectResult == PosPrintResult.success}');
      // if (connectResult.value == 1) { // TODO: chuyen thanh 1
      if (connectResult == PosPrintResult.success) {

        // Tính toán max width dựa trên font size (cải thiện)
        int getMaxWidthForFontSize(PosTextSize fontSize) {
          switch (fontSize) {
            case PosTextSize.size1:
              return 36; // Giữ nguyên
            case PosTextSize.size2:
              return 20; // Giảm từ 28 xuống 18 (chia ~2)
            case PosTextSize.size3:
              return 14; // Giảm từ 20 xuống 12 (chia ~3)
            case PosTextSize.size4:
              return 9; // Giảm từ 16 xuống 9 (chia ~4)
            default:
              return 36;
          }
        }

        int getMaxWidthForModifier(PosTextSize fontSize) {
          switch (fontSize) {
            case PosTextSize.size1:
              return 36;
            case PosTextSize.size2:
              return 19;
            case PosTextSize.size3:
              return 14;
            case PosTextSize.size4:
              return 9;
            default:
              return 36;
          }
        }
        // int getMaxWidthForModifier(PosTextSize fontSize) {
        //   // Modifier luôn có cùng max width với product name để alignment đúng
        //   return getMaxWidthForFontSize(fontSize);
        // }

        // Text wrapping functions với dynamic width
        List<String> wrapItemName(String text, int maxWidth) {
          if (text.isEmpty) return [''];

          final List<String> lines = [];
          String currentLine = '';
          final words = text.split(' ');

          for (final String word in words) {
            final testLine = currentLine.isEmpty ? word : '$currentLine $word';
            if (testLine.length <= maxWidth) {
              currentLine = testLine;
            } else {
              if (currentLine.isNotEmpty) {
                lines.add(currentLine);
                currentLine = word;
              } else {
                // Word quá dài, cắt nó
                if (word.length <= maxWidth) {
                  lines.add(word);
                  currentLine = '';
                } else {
                  // Cắt word dài thành nhiều pieces
                  int start = 0;
                  while (start < word.length) {
                    final int end = (start + maxWidth).clamp(0, word.length);
                    lines.add(word.substring(start, end));
                    start = end;
                  }
                  currentLine = '';
                }
              }
            }
          }
          if (currentLine.isNotEmpty) lines.add(currentLine);
          return lines.isNotEmpty ? lines : [''];
        }

        List<String> wrapModifiers(String text, int maxWidth) {
          if (text.isEmpty) return [''];

          final List<String> lines = [];
          String currentLine = '';
          final words = text.split(' ');

          for (final String word in words) {
            final testLine = currentLine.isEmpty ? word : '$currentLine $word';
            if (testLine.length <= maxWidth) {
              currentLine = testLine;
            } else {
              if (currentLine.isNotEmpty) {
                lines.add(currentLine);
                currentLine = word;
              } else {
                // Word quá dài, cắt nó
                if (word.length <= maxWidth) {
                  lines.add(word);
                  currentLine = '';
                } else {
                  // Cắt word dài thành nhiều pieces
                  int start = 0;
                  while (start < word.length) {
                    final int end = (start + maxWidth).clamp(0, word.length);
                    lines.add(word.substring(start, end));
                    start = end;
                  }
                  currentLine = '';
                }
              }
            }
          }
          if (currentLine.isNotEmpty) lines.add(currentLine);
          return lines.isNotEmpty ? lines : [''];
        }

        /// HEADER
        printer
          ..reset()
          ..setGlobalCodeTable('CP1252')
          ..rawBytes([0x1B, 0x61, 0x00]);

        // Safe text printing cho header
        try {
          printer.text(
            _sanitizeText('# ${orderPrint.orderNumber ?? ''}'),
            styles: const PosStyles(
              align: PosAlign.left,
              bold: true,
              width: PosTextSize.size3,
              height: PosTextSize.size3,
            ),
          );
        } catch (e) {
          print('Lỗi in order number: $e');
          printer.text('# ORDER', styles: const PosStyles(bold: true));
        }

        printer..hr(ch: '-', linesAfter: 0)
          ..rawBytes([0x1B, 0x61, 0x01])
          // setCenterAlignment(printerType ?? 'STAR', printer);
          // printer.rawBytes([0x1B, 0x47, 0x53, 0x01]);
          ..text(
            _sanitizeText(orderType.toUpperCase()),
            styles: const PosStyles(align: PosAlign.center),
          )
          ..hr(ch: '-', linesAfter: 1)
          ..text(
            _sanitizeText(customerName),
            styles: const PosStyles(
              align: PosAlign.center,
              bold: true,
              width: PosTextSize.size2,
              height: PosTextSize.size2,
            ),
          )
          ..feed(1)
          ..rawBytes([0x1B, 0x61, 0x00]);
          if (orderPrint.type?.toUpperCase() == OrderType.DELIVERY.name) {
            printWrappedText(
              printer,
              _sanitizeText(l10n.confirmedBySystem),
              styles: const PosStyles(
                align: PosAlign.left,
              ),
            );
          }
          printWrappedText(
            printer,
            // _sanitizeText('$formattedDate, $formattedTime'),
            _sanitizeText(orderTimeFormat),
            styles: const PosStyles(
              align: PosAlign.left,
            ),
          );
          // ..row([
          //   PosColumn(
          //     text: _sanitizeText(l10n.confirmedBySystem),
          //     width: 8,
          //     styles: const PosStyles(align: PosAlign.left),
          //   ),
          //   PosColumn(
          //     text: formattedDate,
          //     width: 4,
          //     styles: const PosStyles(align: PosAlign.right),
          //   ),
          // ])
          // printer..row([
          //   PosColumn(
          //     text: _sanitizeText('${l10n.invoice}: #'),
          //     width: 5,
          //     styles: const PosStyles(align: PosAlign.left),
          //   ),
          //   PosColumn(
          //     text: formattedTime,
          //     width: 7,
          //     styles: const PosStyles(align: PosAlign.right),
          //   ),
          // ])
          printer..rawBytes([0x1B, 0x61, 0x00])
          ..hr(ch: '_', linesAfter: 0)
          ..feed(1);

        // In danh sách items với dynamic width handling
        for (int index = 0; index < receiptItems.length; index++) {
          final item = receiptItems[index];
          final itemNote = item.notes ?? '';

          try {
            // Safe text processing
            final String itemTitle = _sanitizeText(item.title);
            final String quantity = '${item.quantity ?? 1}';

            if (itemTitle.isEmpty) {
              continue; // Skip items with empty title
            }

            // Tính toán width dựa trên font size
            final int maxWidthForTitle =
                getMaxWidthForFontSize(fontSizeProductName);
            final int maxWidthForModifier =
                getMaxWidthForModifier(fontSizeModifier);

            final titleLines = wrapItemName(itemTitle, maxWidthForTitle);

            // In dòng đầu tiên với quantity
            printer.row([
              PosColumn(
                text: quantity,
                width: isPrintTest ? 1 : fontSize > 1 ? 2 : 1, // Tăng width cho quantity column
                styles: PosStyles(
                  align: PosAlign.left,
                  bold: true,
                  width: fontSizeProductName,
                  height: fontSizeProductName,
                ),
              ),
              PosColumn(
                text: titleLines.isNotEmpty ? titleLines.first : itemTitle,
                width: isPrintTest ? 11 : fontSize > 1
                    ? 10
                    : 11, // Giảm width cho title column để balance
                styles: PosStyles(
                  align: PosAlign.left,
                  bold: true,
                  width: fontSizeProductName,
                  height: fontSizeProductName,
                ),
              ),
            ]);

            // In TẤT CẢ các dòng tiếp theo của title (không bị limit)
            for (int i = 1; i < titleLines.length; i++) {
              printer.row([
                PosColumn(
                  text: '',
                  width: isPrintTest ? 1 : fontSize > 1 ? 2 : 1,
                  styles: PosStyles(
                    width: fontSizeProductName,
                    height: fontSizeProductName,
                  ),
                ),
                PosColumn(
                  text: titleLines[i],
                  width: isPrintTest ? 11 : fontSize > 1 ? 10 : 11,
                  styles: PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: fontSizeProductName,
                    height: fontSizeProductName,
                  ),
                ),
              ]);
            }
            if (fontSize > 1) {
              printer.feed(1);
            }

            // In TẤT CẢ modifiers với error handling
            if (item.modifiers != null && item.modifiers!.isNotEmpty) {
              for (final modifier in item.modifiers!) {
                try {
                  final String modifierText =
                      _sanitizeText(modifier.option?.name);
                  if (modifierText.isNotEmpty) {
                    final modifierLines =
                        wrapModifiers(modifierText, maxWidthForModifier);

                    // In TỪNG DÒNG của modifier giống như ProductName
                    for (final line in modifierLines) {
                      if (line.isNotEmpty) {
                        printer.row([
                          PosColumn(
                            text: '', // Empty space để canh lề
                            width: isPrintTest ? 1 : fontSize > 1 ? 2 : 1,
                            styles: PosStyles(
                              width: fontSizeModifier,
                              height: fontSizeModifier,
                            ),
                          ),
                          PosColumn(
                            text: line, // MỖI dòng riêng biệt
                            // text: 'test xem no dai thi se nhu the nao, dai oi la dai luon, dai sml', // MỖI dòng riêng biệt
                            width: isPrintTest ? 11 : fontSize > 1 ? 10 : 11, // Giữ nguyên 10
                            styles: PosStyles(
                              align: PosAlign.left,
                              width: fontSizeModifier,
                              height: fontSizeModifier,
                            ),
                          ),
                        ]);
                      }
                    }
                  }
                } catch (e) {
                  print('Lỗi in modifier cho item $index: $e');
                }
                if (fontSize > 1) {
                  printer.row(
                    [
                      PosColumn(
                        text: '',
                        width: 12,
                        styles: const PosStyles(
                          width: PosTextSize.size1,
                          height: PosTextSize.size1,
                        ),
                      ),
                    ],
                  );
                }
              }
            }
            
            // In note
            if (itemNote.isNotEmpty) {
              try {
                  final String textNote = _sanitizeText(itemNote);
                  if (textNote.isNotEmpty) {
                    printer
                    ..row(
                      [
                        PosColumn(
                          text: '',
                          width: 12,
                          styles: PosStyles(
                            width: fontSizeModifier,
                            height: fontSizeModifier,
                          ),
                        ),
                      ],
                    )
                    ..row(
                      [
                        PosColumn(
                          text: '',
                          width: isPrintTest ? 1 : fontSize > 1 ? 2 : 1,
                          styles: PosStyles(
                            width: fontSizeModifier,
                            height: fontSizeModifier,
                          ),
                        ),
                        PosColumn(
                          text: l10n.note,
                          width: isPrintTest ? 11 : fontSize > 1 ? 10 : 11,
                          styles: PosStyles(
                            underline: true,
                            bold: true,
                            width: fontSizeModifier,
                            height: fontSizeModifier,
                          ),
                        ),
                      ],
                    );

                    final modifierLines = wrapModifiers(textNote, maxWidthForModifier);
                    // In TỪNG DÒNG của modifier giống như ProductName
                    for (final line in modifierLines) {
                      if (line.isNotEmpty) {
                        printer.row([
                          PosColumn(
                            text: '', // Empty space để canh lề
                            width: isPrintTest ? 1 : fontSize > 1 ? 2 : 1,
                            styles: PosStyles(
                              width: fontSizeModifier,
                              height: fontSizeModifier,
                            ),
                          ),
                          PosColumn(
                            text: line, // MỖI dòng riêng biệt
                            // text: 'test xem no dai thi se nhu the nao, dai oi la dai luon, dai sml', // MỖI dòng riêng biệt
                            width: isPrintTest ? 11 : fontSize > 1 ? 10 : 11, // Giữ nguyên 10
                            styles: PosStyles(
                              align: PosAlign.left,
                              width: fontSizeModifier,
                              height: fontSizeModifier,
                            ),
                          ),
                        ]);
                      }
                    }
                  }
                } catch (e) {
                  print('Lỗi in modifier cho item $index: $e');
                }
            }

            // Add separator after each item
            printer
              ..hr(ch: '_', linesAfter: 0)
              ..feed(1);
          } catch (e) {
            print('Lỗi in item $index: $e');
            // In một dòng backup
            try {
              printer
                ..text('Item ${index + 1}: Error printing')
                ..feed(1);
            } catch (backupError) {
              print('Lỗi in backup text: $backupError');
            }
          }
        }

        // Footer
        try {
          printer
            ..rawBytes([0x1B, 0x61, 0x01])
            ..text(
              _sanitizeText(
                '${getTotalQuantity(orderPrint) > 1 ? l10n.odHistoryTotalItems : l10n.odHistoryTotalItem}: ${getTotalQuantity(orderPrint)}',
              ),
              styles: const PosStyles(
                align: PosAlign.center,
                bold: true,
              ),
            )
            ..cut()
            ..disconnect();
        } catch (e) {
          print('Lỗi in footer: $e');
          printer.cut(); // Ít nhất cũng cắt giấy
        } finally {
          printer.disconnect();
        }
      } else {
        print('Không thể kết nối máy in');
        // printer.disconnect();
      }
    } catch (e) {
      print('Lỗi in hóa đơn: $e');
      rethrow; // Re-throw để caller có thể handle
    }
  }

  // Helper methods để lấy font size (cập nhật)
  PosTextSize _onGetFontSizeProductName(int fontSize) {
    return _getProductNameSize(fontSize);
  }

  PosTextSize _onGetFontSizeModifier(int fontSize) {
    return _getModifierSize(fontSize);
  }

  void setCenterAlignment(String printerType, NetworkPrinter printer) {
    if (printerType.toUpperCase() == 'STAR') {
      // STAR printers
      printer.rawBytes([0x1B, 0x1D, 0x61, 0x01]); // STAR center command
    } else {
      // EPSON printers (default)
      printer.rawBytes([0x1B, 0x61, 0x01]); // EPSON center command
    }
  }
  
  void setLeftAlignment(String printerType, NetworkPrinter printer) {
    if (printerType.toUpperCase() == 'STAR') {
      printer.rawBytes([0x1B, 0x1D, 0x61, 0x00]); // STAR left
    } else {
      printer.rawBytes([0x1B, 0x61, 0x00]); // EPSON left
    }
  }

  String centerText({required String text, int totalWidth = 36}) {
    if (text.length >= totalWidth) return text;
    final int padding = (totalWidth - text.length) ~/ 2;
    return ' ' * padding + text;
  }
  
  int getTotalQuantity(Order order) {
    final items = order.items ?? [];
    return items.fold(0, (sum, item) => sum + (item.quantity ?? 0));
  }

  // Helper function để wrap text cho printer.text()
  void printWrappedText(
    NetworkPrinter printer,
    String text, {
    int maxWidth = 36, // Default cho paper 80mm
    PosStyles? styles,
  }) {
    if (text.isEmpty) return;
    
    final wrappedLines = wrapText(text, maxWidth);
    
    for (final line in wrappedLines) {
      if (line.isNotEmpty) {
        printer.text(line, styles: styles ?? const PosStyles());
      }
    }
  }

  // Hàm wrap text đơn giản
  List<String> wrapText(String text, int maxWidth) {
    if (text.isEmpty) return [''];
    
    final List<String> lines = [];
    String currentLine = '';
    final words = text.split(' ');

    for (final String word in words) {
      final testLine = currentLine.isEmpty ? word : '$currentLine $word';
      if (testLine.length <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine.isNotEmpty) {
          lines.add(currentLine);
          currentLine = word;
        } else {
          // Word quá dài, cắt nó
          if (word.length <= maxWidth) {
            lines.add(word);
            currentLine = '';
          } else {
            int start = 0;
            while (start < word.length) {
              final int end = (start + maxWidth).clamp(0, word.length);
              lines.add(word.substring(start, end));
              start = end;
            }
            currentLine = '';
          }
        }
      }
    }
    if (currentLine.isNotEmpty) lines.add(currentLine);
    return lines.isNotEmpty ? lines : [''];
  }

  // Function để in "Note:" và content cùng lề trái
void printNoteWithContent(
  NetworkPrinter printer,
  String noteLabel, // "Note:" hoặc "Ghi chú:"
  String noteContent, {
  int maxWidth = 36,
  PosStyles? contentStyles,
}) {
  // Tính toán available width cho content trên dòng đầu
  final firstLineMaxWidth = maxWidth - noteLabel.length;
  
  // Wrap content với maxWidth đầy đủ (không trừ noteLabel)
  final wrappedContent = wrapText(noteContent, maxWidth);
  
  if (wrappedContent.isNotEmpty) {
    // Dòng đầu tiên: "Note: " + content
    final firstContentLine = wrappedContent[0];
    
    // Check xem dòng đầu có fit không
    if (firstContentLine.length <= firstLineMaxWidth) {
      // Fit được: in "Note: content" trên 1 dòng
      printer.text(
        '$noteLabel$firstContentLine',
        styles: const PosStyles(),
        linesAfter: 0,
      );
      
      // In các dòng còn lại sát lề trái (không indent)
      for (int i = 1; i < wrappedContent.length; i++) {
        if (wrappedContent[i].isNotEmpty) {
          printer.text(
            wrappedContent[i], 
            styles: contentStyles ?? const PosStyles(),
          );
        }
      }
    } else {
      // Không fit: in "Note:" riêng, content xuống dòng sát lề
      printer.text(
        noteLabel,
        styles: const PosStyles(underline: true, bold: true),
      );
      
      // In tất cả content lines sát lề trái
      for (final line in wrappedContent) {
        if (line.isNotEmpty) {
          printer.text(
            line,
            styles: contentStyles ?? const PosStyles(),
          );
        }
      }
    }
  } else {
    // Content rỗng: chỉ in note label
    printer.text(
      noteLabel,
      styles: const PosStyles(underline: true, bold: true),
    );
  }
}

  void printNoteWithContent1(
    String noteTitle,
    NetworkPrinter printer,
    String noteContent, {
    int maxWidth = 36,
    PosStyles? contentStyles,
  }) {
    // In "Note:" với underline
    printer.text(
      'Note: ',
      styles: const PosStyles(
        underline: true,
        bold: true,
      ),
      linesAfter: 0, // Không xuống dòng sau "Note:"
    );
    
    // Wrap content và in từng dòng
    // final wrappedContent = wrapText(noteContent, maxWidth - 6); // -6 vì "Note: " chiếm 6 ký tự
    final wrappedContent = wrapText(noteContent, maxWidth - noteTitle.length); // -6 vì "Note: " chiếm 6 ký tự
    
    for (int i = 0; i < wrappedContent.length; i++) {
      final line = wrappedContent[i];
      if (line.isNotEmpty) {
        if (i == 0) {
          // Dòng đầu: in liền sau "Note: "
          printer.text(line, styles: contentStyles ?? const PosStyles());
        } else {
          // Các dòng tiếp theo: thêm spaces để align với content
          printer.text('      $line', styles: contentStyles ?? const PosStyles()); // 6 spaces
        }
      }
    }
  }

  // Helper function để tạo image từ text nghiêng
  Future<img.Image?> createItalicTextImage(
    String text, {
    PosTextSize posSize = PosTextSize.size1,
    Color textColor = Colors.black,
    Color backgroundColor = Colors.white,
    int imageWidth = 576, // Width cho paper 80mm
    String fontFamily = 'Arial',
  }) async {
    final fontSize = getFontSizeFromPosTextSize(posSize);
    // Tạo TextPainter
    final textPainter = TextPainter(
      text: TextSpan(
        text: text,
        style: TextStyle(
          fontSize: fontSize,
          color: textColor,
          fontStyle: FontStyle.italic, // Italic style
          fontFamily: fontFamily,
          fontWeight: FontWeight.normal,
        ),
      ),
      textDirection: TextDirection.ltr,
    )

    ..layout(maxWidth: imageWidth.toDouble());

    // Tính toán kích thước image
    final imageHeight = (textPainter.height + 20).toInt(); // +20 cho padding
    
    // Tạo canvas
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder)
    
    // Fill background
    ..drawRect(
      Rect.fromLTWH(0, 0, imageWidth.toDouble(), imageHeight.toDouble()),
      Paint()..color = backgroundColor,
    );
    
    // Vẽ text lên canvas
    textPainter.paint(canvas, const Offset(10, 10)); // 10px padding
    
    // Chuyển thành image
    final picture = recorder.endRecording();
    final ui.Image uiImage = await picture.toImage(imageWidth, imageHeight);
    
    // Chuyển sang format cho thermal printer
    final ByteData? byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
    if (byteData == null) return null;
    
    final Uint8List pngBytes = byteData.buffer.asUint8List();
    return img.decodeImage(pngBytes);
  }

  double getFontSizeFromPosTextSize(PosTextSize posSize) {
    switch (posSize) {
      case PosTextSize.size1:
        return 28.0; // Normal size - khoảng 12px
      case PosTextSize.size2:
        return 30.0; // 2x normal - khoảng 24px
      case PosTextSize.size3:
        return 32.0; // 3x normal - khoảng 36px
      case PosTextSize.size4:
        return 36.0; // 4x normal - khoảng 48px
      default:
        return 28.0; // Default
    }
  }

  // Simple function để tạo image từ text - work cho tất cả platform
  Future<img.Image?> createSimpleTextImage(
    String text, {
    double fontSize = 16,
    bool isItalic = false,
    bool isBold = false,
    PosAlign align = PosAlign.left,
  }) async {
    try {
      final textPainter = TextPainter(
        text: TextSpan(
          text: text,
          style: TextStyle(
            fontSize: fontSize,
            color: Colors.black,
            fontStyle: isItalic ? FontStyle.italic : FontStyle.normal,
            fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: _getTextAlign(align),
      );

      textPainter.layout(maxWidth: 576); // Safe width cho 80mm paper
      
      // Simple dimensions
      final imageWidth = 576; // 288 pixels = ~80mm
      final imageHeight = (textPainter.height + 10).ceil(); // Minimal padding
      
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      
      // White background
      canvas.drawRect(
        Rect.fromLTWH(0, 0, imageWidth.toDouble(), imageHeight.toDouble()),
        Paint()..color = Colors.white,
      );
      
      // Calculate x position based on alignment
      double xOffset = 5; // Default left padding
      if (align == PosAlign.center) {
        xOffset = (imageWidth - textPainter.width) / 2;
      } else if (align == PosAlign.right) {
        xOffset = imageWidth - textPainter.width - 5;
      }
      
      // Draw text
      textPainter.paint(canvas, Offset(xOffset, 5));
      
      final picture = recorder.endRecording();
      final ui.Image uiImage = await picture.toImage(imageWidth, imageHeight);
      
      // Convert to PNG bytes (most compatible)
      final ByteData? byteData = await uiImage.toByteData(format: ui.ImageByteFormat.png);
      if (byteData == null) return null;
      
      final Uint8List pngBytes = byteData.buffer.asUint8List();
      return img.decodePng(pngBytes);
      
    } catch (e) {
      print('Error creating text image: $e');
      return null;
    }
  }

  // Helper function
  TextAlign _getTextAlign(PosAlign posAlign) {
    switch (posAlign) {
      case PosAlign.center:
        return TextAlign.center;
      case PosAlign.right:
        return TextAlign.right;
      default:
        return TextAlign.left;
    }
  }

  // Main function để in text as image
  Future<void> printTextAsImage(
    NetworkPrinter printer,
    String text, {
    PosTextSize textSize = PosTextSize.size1,
    bool isItalic = false,
    bool isBold = false,
    PosAlign align = PosAlign.left,
  }) async {
    try {
      final fontSize = _getFontSize(textSize);
      final image = await createSimpleTextImage(
        text,
        fontSize: fontSize,
        isItalic: isItalic,
        isBold: isBold,
        align: align,
      );
      
      if (image != null) {
        printer.image(image, align: align);
      } else {
        // Fallback to normal text
        printer.text(text, styles: PosStyles(
          align: align,
          bold: isBold,
          width: textSize,
          height: textSize,
        ));
      }
    } catch (e) {
      print('Error printing text as image: $e');
      // Ultimate fallback
      printer.text(text);
    }
  }

  double _getFontSize(PosTextSize textSize) {
    switch (textSize) {
      case PosTextSize.size1:
        return 28.0; // Normal size - khoảng 12px
      case PosTextSize.size2:
        return 30.0; // 2x normal - khoảng 24px
      case PosTextSize.size3:
        return 32.0; // 3x normal - khoảng 36px
      case PosTextSize.size4:
        return 36.0; // 4x normal - khoảng 48px
      default:
        return 28.0; // Default
    }
  }


}
