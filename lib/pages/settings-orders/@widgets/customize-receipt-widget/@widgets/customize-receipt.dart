import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customize-customer-receipt/customize-customer-receipt.cubit.dart';
import 'package:stickyqrbusiness/@widgets/avatar-control-widget.dart';
import 'package:stickyqrbusiness/@widgets/button-control-widget.dart';
import 'package:stickyqrbusiness/@widgets/button-loading-widget.dart';
import 'package:stickyqrbusiness/@widgets/custom-loading.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-receipt-preview.dart';

class CustomizeReceiptCustomerWidget extends StatefulWidget {
  const CustomizeReceiptCustomerWidget({super.key});

  @override
  State<CustomizeReceiptCustomerWidget> createState() => _CustomizeReceiptCustomerWidgetState();
}

class _CustomizeReceiptCustomerWidgetState extends State<CustomizeReceiptCustomerWidget>
    with WidgetsBindingObserver {
  late final l10n = context.l10n;
  final TextEditingController customTextController = TextEditingController();
  final FocusNode _textFieldFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    _textFieldFocusNode.dispose();
    customTextController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.sizeOf(context).width >= 600;
    return BlocListener<CustomizeCustomerReceiptCubit,
        CustomizeCustomerReceiptState>(
      listener: (context, state) {
        if (state.status == CustomizeCustomerReceiptStatus.Updated) {
          AppBased.toastSuccess(context, title: l10n.successfully, duration: 1);
          context.read<CustomizeCustomerReceiptCubit>().onResetStatus(status: CustomizeCustomerReceiptStatus.Success);
        }
        if (state.status == CustomizeCustomerReceiptStatus.Success) {
          if (state.receiptTemplate != null) {
            final logoBusinessID =
                context.read<AuthBloc>().state.logoBusinessID ?? '';
            context.read<CustomizeCustomerReceiptCubit>().onChangedFieldUpdate(
                  logoId: logoBusinessID,
                  isShowBusinessLogo: state.receiptTemplate?.isShowBusinessLogo,
                  isShowBusinessName: state.receiptTemplate?.isShowBusinessName,
                  isShowBusinessContact:
                      state.receiptTemplate?.isShowBusinessContact,
                  isShowCustomerInfo: state.receiptTemplate?.isShowCustomerInfo,
                  isShowOrderInfo: state.receiptTemplate?.isShowOrderInfo,
                  isShowOrderModifier:
                      state.receiptTemplate?.isShowOrderModifier,
                  footerText: state.receiptTemplate?.footerText ?? l10n.thanksYou,
                );
                customTextController.text = state.receiptTemplate?.footerText ?? l10n.thanksYou;
          }
        } else if (state.status == CustomizeCustomerReceiptStatus.Error) {
          if (state.errorMsg != null && state.errorMsg != '') {
            AppBased.toastError(context, title: state.errorMsg);
            context.read<CustomizeCustomerReceiptCubit>().onResetStatus();
          }
        }
      },
      child: GestureDetector(
        onTap: () {
           FocusScope.of(context).unfocus();
          _textFieldFocusNode.unfocus();
        },
        child: SafeArea(
          left: false,
          right: false,
          child: Scaffold(
            resizeToAvoidBottomInset: true,
            // backgroundColor: const Color(0xFFF5F5F5),
            body: BlocBuilder<CustomizeCustomerReceiptCubit,
                CustomizeCustomerReceiptState>(
              builder: (context, state) {
                switch (state.status) {
                  case CustomizeCustomerReceiptStatus.Loading:
                    return _buildLoading();
        
                  case CustomizeCustomerReceiptStatus.Success:
                  case CustomizeCustomerReceiptStatus.Edit:
                  case CustomizeCustomerReceiptStatus.Inprogress:
                  case CustomizeCustomerReceiptStatus.Updated:
                    if (state.receiptTemplate != null) {
                      // return _buildLoading();
                      return isTablet
                          ? _buildContentTablet()
                          : _buildContentMobile();
                    }
                    return Center(
                      child: Text(
                        l10n.noReceiptFound,
                        style: const TextStyle(fontSize: 16),
                      ),
                    );
        
                  case CustomizeCustomerReceiptStatus.Error:
                    return _buildErrorWidget(errMsg: state.errorMsg);
        
                  default:
                    return const SizedBox.shrink();
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContentMobile() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: ListView(
              padding: const EdgeInsets.all(16.0),
              shrinkWrap: true,
              children: [
                _buildBusinessInfo(),
                _buildOrderInfo(),
                // _buildLoyaltyQRCode(),
                _buildAdditionalText(),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Expanded(
                child: ButtonControlWidget(
                  onPressed: () {
                    _showPreview(context);
                  },
                  buttonText: l10n.preview,
                  buttonTextColor: Colors.black,
                  height: 48,
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xFF999CA0),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              BlocBuilder<CustomizeCustomerReceiptCubit, CustomizeCustomerReceiptState>(
                builder: (context, state) {
                  return Expanded(
                    flex: 1,
                    child: ButtonLoading(
                      callback: () {
                        context
                            .read<CustomizeCustomerReceiptCubit>()
                            .onUpdateReceiptTemplate();
                      },
                      isLoading:
                          state.status == CustomizeCustomerReceiptStatus.Inprogress
                              ? true
                              : false,
                      label: l10n.save,
                      labelColor: Colors.white,
                      buttonBackgroundColor: const Color(0xFF0B0B0B),
                      height: 48,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContentTablet() {
    return Stack(
          children: [
            Positioned.fill(
              bottom: 56,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: SafeArea(
                  child: Column(
                    children: [
                      _buildBusinessInfo(),
                      _buildOrderInfo(),
                      _buildAdditionalText(),
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.white,
                  border: Border(top: BorderSide(width: 1, color: AppColors.appBorderColor)),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: BlocBuilder<CustomizeCustomerReceiptCubit, CustomizeCustomerReceiptState>(
                    builder: (context, state) {
                      return IntrinsicWidth(
                        child: ButtonLoading(
                          callback: () {
                            context
                                .read<CustomizeCustomerReceiptCubit>()
                                .onUpdateReceiptTemplate();
                          },
                          isLoading: state.status == CustomizeCustomerReceiptStatus.Inprogress
                              ? true
                              : false,
                          label: l10n.save,
                          labelColor: Colors.white,
                          buttonBackgroundColor: const Color(0xFF0B0B0B),
                          height: 48,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
  // return Row(
  //   children: [
  //     Expanded(
  //       flex: 4,
  //       child: Stack(
  //         children: [
  //           Positioned.fill(
  //             bottom: 80, // Để chỗ cho button (height 56 + padding)
  //             child: SingleChildScrollView(
  //               padding: const EdgeInsets.all(16.0),
  //               child: Column(
  //                 children: [
  //                   _buildBusinessInfo(),
  //                   _buildOrderInfo(),
  //                   _buildAdditionalText(),
  //                 ],
  //               ),
  //             ),
  //           ),
  //           Positioned(
  //             left: 0,
  //             right: 0,
  //             bottom: 0,
  //             child: Container(
  //               decoration: const BoxDecoration(
  //                 color: Colors.white,
  //                 border: Border(top: BorderSide(width: 1, color: AppColors.appBorderColor)),
  //               ),
  //               padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
  //               child: Align(
  //                 alignment: Alignment.centerRight,
  //                 child: BlocBuilder<CustomizeCustomerReceiptCubit, CustomizeCustomerReceiptState>(
  //                   builder: (context, state) {
  //                     return IntrinsicWidth(
  //                       child: ButtonLoading(
  //                         callback: () {
  //                           context
  //                               .read<CustomizeCustomerReceiptCubit>()
  //                               .onUpdateReceiptTemplate();
  //                         },
  //                         isLoading: state.status == CustomizeCustomerReceiptStatus.Inprogress
  //                             ? true
  //                             : false,
  //                         label: l10n.save,
  //                         labelColor: Colors.white,
  //                         buttonBackgroundColor: const Color(0xFF0B0B0B),
  //                         height: 56,
  //                       ),
  //                     );
  //                   },
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //     const Expanded(flex: 3, child: CustomizeReceiptPreviewWidget()),
  //   ],
  // );
}

  /// BUSINESS INFO SETTING
  Widget _buildBusinessInfo() {
    return BlocBuilder<CustomizeCustomerReceiptCubit,
        CustomizeCustomerReceiptState>(
      builder: (context, state) {
        final businessLogo = context.read<AuthBloc>().state.logoBusiness ?? '';
        // final orverideLogo = state.logoId ?? businessLogo;
        // final isShowLogo = state.logoId != '';
        final isShowLogo = state.isShowBusinessLogo;
        final isShowBusName = state.isShowBusinessName;
        final isShowBusContact = state.isShowBusinessContact;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              l10n.receiptBusinessInfo,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.appBorderColor),
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _buildCustomSwitch(
                    title: 'Logo',
                    subtitle: l10n.receiptLogoPrinted,
                    value: isShowLogo,
                    onChanged: (value) {
                      print('value = $value   // businessLogo = $businessLogo');
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowLogo(value);
                    },
                  ),
                  _buildUploadLogo(),
                  _buildDivider(),
                  _buildCustomSwitch(
                    title: l10n.businessName,
                    value: isShowBusName,
                    onChanged: (value) {
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowBusinessName(value);
                    },
                  ),
                  _buildDivider(),
                  _buildCustomSwitch(
                    title: l10n.receiptContact,
                    value: isShowBusContact,
                    onChanged: (value) {
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowBusinessContact(value);
                    },
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// ORDER INFO SETTING
  Widget _buildOrderInfo() {
    return BlocBuilder<CustomizeCustomerReceiptCubit,
        CustomizeCustomerReceiptState>(
      builder: (context, state) {
        final isShowCustomerName = state.isShowCustomerInfo;
        final isShowOrderInfo = state.isShowOrderInfo;
        final isShowOrderModifier = state.isShowOrderModifier;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 24.0),
              child: Text(
                l10n.receiptOrderInfo,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.appBorderColor),
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                children: [
                  _buildCustomSwitch(
                    title: l10n.receiptCustomerName,
                    value: isShowCustomerName,
                    onChanged: (value) {
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowCustomerInfo(value);
                    },
                  ),
                  _buildDivider(),
                  _buildCustomSwitch(
                    title: l10n.receiptOrderInfo,
                    value: isShowOrderInfo,
                    onChanged: (value) {
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowOrderInfo(value);
                    },
                  ),
                  _buildDivider(),
                  _buildCustomSwitch(
                    title: l10n.showModifiers,
                    value: isShowOrderModifier,
                    onChanged: (value) {
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedIsShowOrderModifier(value);
                    },
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  /// LOYALTY SETTING
  // Widget _buildLoyaltyQRCode() {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.stretch,
  //     children: [
  //       const Padding(
  //         padding: EdgeInsets.only(top: 24.0),
  //         child: Text(
  //           'Loyalty QR Code',
  //           style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
  //         ),
  //       ),
  //       Container(
  //         decoration: BoxDecoration(
  //           border: Border.all(color: AppColors.appBorderColor),
  //           borderRadius: BorderRadius.circular(12),
  //         ),
  //         margin: const EdgeInsets.symmetric(vertical: 8.0),
  //         child: Column(
  //           children: [
  //             _buildCustomSwitch(
  //               title: 'Show Loyalty QR Code',
  //               value: modifierEnabled,
  //               onChanged: (value) {
  //                 setState(() {
  //                   modifierEnabled = value;
  //                 });
  //               },
  //             ),
  //             RadioListTile<String>(
  //               title: const Text('Give Points'),
  //               value: 'Give Points',
  //               groupValue: loyaltyOption,
  //               activeColor: Colors.black,
  //               onChanged: (value) {
  //                 setState(() {
  //                   loyaltyOption = value!;
  //                 });
  //               },
  //             ),
  //             _buildDivider(),
  //             RadioListTile<String>(
  //               title: const Text(
  //                   'Give Vouchers\nGet \$10 OFF When You Spend 123 asdlk ajsdlj alsdj alksdj lkajsd lkj'),
  //               value: 'Give Vouchers',
  //               groupValue: loyaltyOption,
  //               activeColor: Colors.black,
  //               onChanged: (value) {
  //                 setState(() {
  //                   loyaltyOption = value!;
  //                 });
  //               },
  //             ),
  //           ],
  //         ),
  //       ),
  //     ],
  //   );
  // }

  /// ADDITIONAL TEXT SETTING
  Widget _buildAdditionalText() {
    return BlocBuilder<CustomizeCustomerReceiptCubit, CustomizeCustomerReceiptState>(
      builder: (context, state) {
        final isShowAdditionalText = state.footerText.isNotEmpty;
        // final additionalText = state.footerText;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 24.0),
              child: Text(
                l10n.receiptAdditionalText,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.appBorderColor),
                borderRadius: BorderRadius.circular(12),
              ),
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              child: Column(
                children: [
                  _buildCustomSwitch(
                    title: l10n.receiptCustomText,
                    value: isShowAdditionalText,
                    onChanged: (value) {
                      print('value = $value');
                      context
                          .read<CustomizeCustomerReceiptCubit>()
                          .onChangedFooterText(value
                              ? customTextController.text != null &&
                                      customTextController.text != ''
                                  ? customTextController.text
                                  : l10n.thanksYou
                              : '');
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 5.0, 16.0, 16.0),
                    child: TextField(
                      controller: customTextController,
                      focusNode: _textFieldFocusNode,
                      onChanged: (value) {
                        context.read<CustomizeCustomerReceiptCubit>().onChangedFooterText(value);
                      },
                      decoration: InputDecoration(
                        border: const OutlineInputBorder(),
                        hintText: l10n.receiptCustomTextExample,
                        hintStyle: TextStyle(color: Colors.grey.shade500),
                        enabledBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: Color(
                              0xFFEBEBEB,
                            ),
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: const BorderSide(
                            color: AppColors.appBlackColor,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildUploadLogo() {
    return BlocBuilder<CustomizeCustomerReceiptCubit, CustomizeCustomerReceiptState>(
      builder: (context, state) {
        // final isShowBusinessLogo = state.isShowBusinessLogo;
        // final logoId = state.logoId ?? '';
        final logoBusiness = context.read<AuthBloc>().state.logoBusiness ?? '';
        // final logoUrl = state.logoId != null ? '${AppBased.appEnv.cdnUrl}${state.logoId}' : logoBusiness;
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (logoBusiness != '')
                SizedBox(
                  width: 180,
                  height: 100,
                  child: DashedBorder(
                    dashWidth: 3.0,
                    dashSpace: 2.0,
                    strokeWidth: 1.0,
                    color: Colors.grey.shade300,
                    child: AvatarControlWidget(
                      name: '',
                      // urlImage: logoUrl,
                      urlImage: logoBusiness,
                      boxFit: BoxFit.contain,
                      width: 179,
                      height: 99,
                      backgroundColor: Colors.transparent,
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              // Container(
              //   height: 100,
              //   width: 100,
              //   decoration: BoxDecoration(
              //     border: Border.all(color: AppColors.appBorderColor),
              //     borderRadius: BorderRadius.circular(12),
              //   ),
              //   child: const Center(
              //     child: Icon(Icons.upload),
              //   ),
              // ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    final width = (MediaQuery.of(context).size.width) * 0.6 - 70;
    final item = Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ItemLoadingCustomWidget(width: width * .6, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
        const SizedBox(height: 48),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
      ],
    );
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 24),
      children: <Widget>[
        LoadingCustomWidget(
          lineItem: 1,
          isBorder: false,
          isPadding: true,
          paddingList: EdgeInsets.zero,
          paddingItem: const EdgeInsets.only(bottom: 8),
          widget: item,
        ),
      ],
    );
  }

  Widget _buildErrorWidget({String? errMsg}) {
    return Center(
      child: Text(
        errMsg ?? l10n.somethingWentWrong,
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  Widget _buildCustomSwitch({
    required String title,
    String? subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    TextStyle? titleStyle,
    TextStyle? subTitleStyle,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: 16.0, right: 8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: titleStyle ??
                        const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                  if (subtitle != null)
                    Text(
                      subtitle,
                      style: subTitleStyle ??
                          const TextStyle(
                            fontSize: 14,
                            color: Colors.black54,
                            fontWeight: FontWeight.w400,
                          ),
                    ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: Transform.scale(
              scale: 0.8,
              child: CupertinoSwitch(
                value: value,
                inactiveTrackColor: AppColors.appBGGreyColor,
                activeTrackColor: const Color(0xFF23A037),
                onChanged: onChanged,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Divider _buildDivider() {
    return const Divider(
      color: AppColors.appBorderColor,
      thickness: 1,
      height: 1,
    );
  }

  Future<void> _showPreview(BuildContext context) async {
    FocusScope.of(context).unfocus();
    _textFieldFocusNode.unfocus();
    
    // Thêm delay nhỏ để đảm bảo unfocus hoàn tất
    // ignore: inference_failure_on_instance_creation
    await Future.delayed(const Duration(milliseconds: 100));
    if (!mounted) return;
    await showGeneralDialog(
      barrierLabel: '',
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.4),
      context: context,
      pageBuilder: (context, anim1, anim2) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Container(
            alignment: Alignment.center,
            padding: MediaQuery.of(context).viewInsets,
            child: const CustomizeReceiptPreviewWidget(isShowAppbar: true),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return SlideTransition(
          position:
              Tween(begin: const Offset(0, 1), end: Offset.zero).animate(anim1),
          child: child,
        );
      },
    );
    if (mounted) {
      FocusScope.of(context).unfocus();
      _textFieldFocusNode.unfocus();
    }
  }

  void _buildCompleted() {
    final authState = context.read<AuthBloc>().state;
    final logoId = authState.business?.logo ?? '';
    context.read<CustomizeCustomerReceiptCubit>().getReceiptTemplates(l10n: l10n, logoId: logoId);
  }
}

class DashedBorder extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;
  final Color color;

  const DashedBorder({
    Key? key,
    required this.child,
    this.width,
    this.height,
    this.dashWidth = 5.0,
    this.dashSpace = 5.0,
    this.strokeWidth = 2.0,
    this.color = Colors.black,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double containerWidth = width ?? constraints.maxWidth;
        final double containerHeight = height ?? constraints.maxHeight;

        return CustomPaint(
          painter: DashedBorderPainter(
            dashWidth: dashWidth,
            dashSpace: dashSpace,
            strokeWidth: strokeWidth,
            color: color,
          ),
          child: SizedBox(
            width: containerWidth,
            height: containerHeight > 0 ? containerHeight : null,
            child: child,
          ),
        );
      },
    );
  }
}

class DashedBorderPainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final double strokeWidth;
  final Color color;

  DashedBorderPainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.strokeWidth,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    double startX = 0.0;
    double startY = 0.0;

    // Vẽ đường nét đứt cho cạnh trên
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0),
        Offset(startX + dashWidth, 0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }

    // Vẽ đường nét đứt cho cạnh dưới
    startX = 0.0;
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, size.height),
        Offset(startX + dashWidth, size.height),
        paint,
      );
      startX += dashWidth + dashSpace;
    }

    // Vẽ đường nét đứt cho cạnh trái
    while (startY < size.height) {
      canvas.drawLine(
        Offset(0, startY),
        Offset(0, startY + dashWidth),
        paint,
      );
      startY += dashWidth + dashSpace;
    }

    // Vẽ đường nét đứt cho cạnh phải
    startY = 0.0;
    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width, startY),
        Offset(size.width, startY + dashWidth),
        paint,
      );
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
