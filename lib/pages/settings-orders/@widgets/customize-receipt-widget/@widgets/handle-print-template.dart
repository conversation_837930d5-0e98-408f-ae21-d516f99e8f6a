// ignore_for_file: inference_failure_on_instance_creation

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:stickyqrbusiness/@core/app-based.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/models/printer.model.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/kitchen-ticket-template.dart';

class PrintReceiptService {
  // Singleton pattern để sử dụng toàn app
  static final PrintReceiptService _instance = PrintReceiptService._internal();
  factory PrintReceiptService() => _instance;
  PrintReceiptService._internal();

  /// In hóa đơn khách hàng
  Future<void> printCustomerReceipt({
    required BuildContext context,
    required AppLocalizations l10n,
    required Order order,
    required List<Printer> printersReceipt,
    required CustomerReceiptTemplateWidget customerPrint,
  }) async {
    // Kiểm tra context có còn mounted không
    if (!context.mounted) {
      AppLog.e('Context is not mounted, skipping print operation');
      return;
    }

    if (printersReceipt.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    }

    for (final printer in printersReceipt) {
      final ip = printer.address ?? '';
      
      // Kiểm tra điều kiện cơ bản
      if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
      
      // Lấy số lượng bản sao cần in
      final receiptCopies = printer.receiptCopies ?? 1;
      final numberOfCopies = receiptCopies > 0 ? receiptCopies : 1;
      
      AppLog.d('Printing $numberOfCopies copies for printer ${printer.address}');
      
      // In theo số lượng bản sao đã định
      for (int copyIndex = 1; copyIndex <= numberOfCopies; copyIndex++) {
        // Kiểm tra context trước mỗi lần in
        if (!context.mounted) {
          AppLog.e('Context unmounted during printing, stopping operation');
          return;
        }

        try {
          await customerPrint.printCustomerReceiptAsText(
            context: context,
            ipAddress: printer.address ?? '',
            order: order,
          );
          
          AppLog.d('Printed copy $copyIndex/$numberOfCopies for printer ${printer.address}');
          
          // Delay nhỏ giữa các lần in
          if (copyIndex < numberOfCopies) {
            await Future.delayed(const Duration(milliseconds: 500));
          }
          
        } catch (e) {
          AppLog.e('Error printing copy $copyIndex for printer ${printer.address}: $e');
          break;
        }
      }
    }
  }

  /// In phiếu bếp
  Future<void> printKitchenTicket({
    required BuildContext context,
    required AppLocalizations l10n,
    required Order order,
    required String timeZone,
    required List<Printer> printersKitchen,
    required KitchenTicketTemplateWidget kitchenPrint,
  }) async {
    // Kiểm tra context có còn mounted không
    if (!context.mounted) {
      AppLog.e('Context is not mounted, skipping print operation');
      return;
    }

    if (printersKitchen.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    }

    for (final printer in printersKitchen) {
      final ip = printer.address ?? '';
      AppLog.e('ip printer = $ip');
      
      // Kiểm tra điều kiện cơ bản
      if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
      
      // Kiểm tra kitchenCategories
      final kitchenCategories = printer.kitchenCategories ?? <String>[];
      
      if (kitchenCategories.isEmpty) {
        AppLog.d('Printer ${printer.address} has empty kitchenCategories, skipping...');
        continue;
      }
      
      // Tạo order mới chỉ chứa các item được phép in
      final filteredOrder = _createFilteredOrder(order, kitchenCategories);
      
      if (filteredOrder == null || _isOrderEmpty(filteredOrder)) {
        AppLog.d('No items to print for printer ${printer.address}');
        continue;
      }
      
      // Kiểm tra context trước khi in
      if (!context.mounted) {
        AppLog.e('Context unmounted during printing, stopping operation');
        return;
      }

      AppLog.e('filteredOrder == ${jsonEncode(filteredOrder)}');
      
      try {
        await kitchenPrint.printKitchenTicketAsText(
          l10n: l10n,
          timezone: timeZone,
          ipAddress: printer.address ?? '',
          order: filteredOrder,
        );
        
        AppLog.d('Printed filtered order for printer ${printer.address}');
      } catch (e) {
        AppLog.e('Error printing kitchen ticket for printer ${printer.address}: $e');
      }
    }
  }

  /// Phương thức tiện ích để in cả hai loại cùng lúc
  Future<void> printAll({
    required BuildContext context,
    required AppLocalizations l10n,
    required Order order,
    required String timeZone,
    required List<Printer> printersReceipt,
    required List<Printer> printersKitchen,
    required CustomerReceiptTemplateWidget customerPrint,
    required KitchenTicketTemplateWidget kitchenPrint,
  }) async {
    if (!context.mounted) return;

    // In song song để tăng hiệu suất
    await Future.wait([
      printCustomerReceipt(
        context: context,
        l10n: l10n,
        order: order,
        printersReceipt: printersReceipt,
        customerPrint: customerPrint,
      ),
      printKitchenTicket(
        context: context,
        l10n: l10n,
        order: order,
        timeZone: timeZone,
        printersKitchen: printersKitchen,
        kitchenPrint: kitchenPrint,
      ),
    ]);
  }

  // Hàm tạo order mới chỉ chứa các item được phép in
  Order? _createFilteredOrder(Order? originalOrder, List<String> allowedCategoryIds) {
    if (originalOrder == null) return null;
    
    // Tạo bản copy của order gốc
    final filteredOrder = originalOrder.copyWith();
    
    // Lọc các item dựa trên categories được phép
    final filteredItems = <ItemOrder>[];
    
    for (final item in originalOrder.items ?? <ItemOrder>[]) {
      final productCategories = item.product?.categories ?? <ProductOrderCetegories>[];
      
      // Kiểm tra xem item có category được phép in không
      bool hasAllowedCategory = false;
      for (final category in productCategories) {
        if (allowedCategoryIds.contains(category.id)) {
          hasAllowedCategory = true;
          break;
        }
      }
      
      // Nếu item có category được phép thì thêm vào danh sách
      if (hasAllowedCategory) {
        filteredItems.add(item);
      }
    }
    
    // Cập nhật danh sách items đã lọc
    return filteredOrder.copyWith(items: filteredItems);
  }

  // Hàm kiểm tra order có rỗng không
  bool _isOrderEmpty(Order order) {
    return order.items == null || order.items!.isEmpty;
  }

}
