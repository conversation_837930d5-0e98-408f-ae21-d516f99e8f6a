import 'dart:convert';

import 'package:diacritic/diacritic.dart';
import 'package:esc_pos_printer_lts/esc_pos_printer_lts.dart';
import 'package:esc_pos_utils_lts/esc_pos_utils_lts.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart' as intl;
import 'package:stickyqrbusiness/@common/_local-storage.dart';
import 'package:stickyqrbusiness/@core/models/order.model.dart';
import 'package:stickyqrbusiness/@core/models/ordering-modifier.model.dart';
import 'package:stickyqrbusiness/@core/models/receipt-template.model.dart';
import 'package:stickyqrbusiness/@core/store/auth/auth_bloc.dart';
import 'package:stickyqrbusiness/@utils/currency-input-formatter/extensions/string-extensions.dart';
import 'package:stickyqrbusiness/@utils/currency.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
// import 'dart:typed_data';

// enum PrinterBrand {
//   epson,
//   star,
//   unknown,
// }

// class PrinterConfig {
//   final PrinterBrand brand;
//   final int paperWidth;
//   final int leftMargin;
//   final int rightMargin;
//   final Map<PosTextSize, int> maxWidths;
//   final Map<PosAlign, List<int>> alignmentCommands;
//   final bool needsExtraSpacing;

//   const PrinterConfig({
//     required this.brand,
//     required this.paperWidth,
//     required this.leftMargin,
//     required this.rightMargin,
//     required this.maxWidths,
//     required this.alignmentCommands,
//     this.needsExtraSpacing = false,
//   });

//   static const PrinterConfig epsonConfig = PrinterConfig(
//     brand: PrinterBrand.epson,
//     paperWidth: 48,
//     leftMargin: 0,
//     rightMargin: 0,
//     maxWidths: {
//       PosTextSize.size1: 48,
//       PosTextSize.size2: 24,
//       PosTextSize.size3: 16,
//       PosTextSize.size4: 12,
//     },
//     alignmentCommands: {
//       PosAlign.left: [0x1B, 0x61, 0x00],
//       PosAlign.center: [0x1B, 0x61, 0x01],
//       PosAlign.right: [0x1B, 0x61, 0x02],
//     },
//     needsExtraSpacing: false,
//   );

//   static const PrinterConfig starConfig = PrinterConfig(
//     brand: PrinterBrand.star,
//     paperWidth: 42, // STAR có paper width nhỏ hơn
//     leftMargin: 2,  // Thêm margin trái để cân bằng
//     rightMargin: 2,
//     maxWidths: {
//       PosTextSize.size1: 42,
//       PosTextSize.size2: 21,
//       PosTextSize.size3: 14,
//       PosTextSize.size4: 10,
//     },
//     alignmentCommands: {
//       PosAlign.left: [0x1B, 0x61, 0x00],
//       PosAlign.center: [0x1B, 0x1D, 0x61, 0x01], // Command khác cho STAR
//       PosAlign.right: [0x1B, 0x61, 0x02],
//     },
//     needsExtraSpacing: true,
//   );

//   static const PrinterConfig defaultConfig = PrinterConfig(
//     brand: PrinterBrand.unknown,
//     paperWidth: 45,
//     leftMargin: 1,
//     rightMargin: 1,
//     maxWidths: {
//       PosTextSize.size1: 45,
//       PosTextSize.size2: 22,
//       PosTextSize.size3: 15,
//       PosTextSize.size4: 11,
//     },
//     alignmentCommands: {
//       PosAlign.left: [0x1B, 0x61, 0x00],
//       PosAlign.center: [0x1B, 0x61, 0x01],
//       PosAlign.right: [0x1B, 0x61, 0x02],
//     },
//     needsExtraSpacing: false,
//   );
// }

// class UniversalPrinterHandler {
//   PrinterConfig _config = PrinterConfig.defaultConfig;
//   late NetworkPrinter _printer;

//   //  Khởi tạo với printer detection
//   Future<void> initialize(String ipAddress, {int port = 9100}) async {
//     final profile = await CapabilityProfile.load();
//     _printer = NetworkPrinter(PaperSize.mm80, profile);
    
//     final connectResult = await _printer.connect(ipAddress, port: port);
//     if (connectResult.value == 1) {
//       // Detect printer brand
//       _config = await _detectPrinterBrand();
//       print('Detected printer: ${_config.brand}');
//     }
//   }

//   // Detect printer brand bằng cách test các commands đặc trung
//   Future<PrinterConfig> _detectPrinterBrand() async {
//     try {
//       // Test STAR-specific command
//       _printer.rawBytes([0x1B, 0x1D, 0x61, 0x01]); // STAR center alignment
//       await Future.delayed(const Duration(milliseconds: 100));
      
//       // Nếu không có lỗi, có thể là STAR
//       return PrinterConfig.starConfig;
//     } catch (e) {
//       // Nếu có lỗi, có thể là EPSON hoặc generic
//       try {
//         _printer.rawBytes([0x1B, 0x74, 0x00]); // EPSON character set
//         return PrinterConfig.epsonConfig;
//       } catch (e) {
//         return PrinterConfig.defaultConfig;
//       }
//     }
//   }

//   // Enhanced text printing with universal alignment
//   void printText(
//     String text, {
//     PosAlign align = PosAlign.left,
//     bool bold = false,
//     PosTextSize size = PosTextSize.size1,
//   }) {
//     // Apply alignment command specific to printer
//     final alignCommand = _config.alignmentCommands[align] ?? 
//                         _config.alignmentCommands[PosAlign.left]!;
//     _printer.rawBytes(alignCommand);

//     // Add margins for STAR printers
//     String processedText = _sanitizeText(text);
//     if (_config.brand == PrinterBrand.star) {
//       switch (align) {
//         case PosAlign.left:
//           processedText = ' ' * _config.leftMargin + text;
//           break;
//         case PosAlign.center:
//           // For center alignment on STAR, manually calculate padding
//           final totalWidth = _config.maxWidths[size] ?? 42;
//           final textLength = text.length;
//           final padding = ((totalWidth - textLength) / 2).floor();
//           processedText = ' ' * padding + text + ' ' * padding;
//           break;
//         case PosAlign.right:
//           processedText = text + ' ' * _config.rightMargin;
//           break;
//       }
//     }

//     _printer.text(
//       processedText,
//       styles: PosStyles(
//         align: PosAlign.left, // Always use left after manual alignment
//         bold: bold,
//         width: size,
//         height: size,
//       ),
//     );

//     // Extra spacing for STAR if needed
//     if (_config.needsExtraSpacing && _config.brand == PrinterBrand.star) {
//       _printer.feed(1);
//     }
//   }

//   // Enhanced row printing with universal column handling
//   void printRow(List<RowColumn> columns) {
//     // Validate and normalize column widths to ensure they stay within 1-12 range
//     int totalRequestedWidth = columns.fold(0, (sum, col) => sum + col.width);
    
//     // If total width exceeds 12, proportionally scale down
//     if (totalRequestedWidth > 12) {
//       final scaleFactor = 12.0 / totalRequestedWidth;
//       for (int i = 0; i < columns.length; i++) {
//         final newWidth = (columns[i].width * scaleFactor).round();
//         columns[i] = RowColumn(
//           text: _sanitizeText(columns[i].text),
//           width: newWidth.clamp(1, 12), // Ensure between 1-12
//           align: columns[i].align,
//           bold: columns[i].bold,
//           textSize: columns[i].textSize,
//         );
//       }
//     }
    
//     // Final validation: ensure sum doesn't exceed 12
//     int currentTotal = columns.fold(0, (sum, col) => sum + col.width);
//     if (currentTotal > 12) {
//       // Reduce the last column's width to fit
//       final lastIndex = columns.length - 1;
//       final excess = currentTotal - 12;
//       columns[lastIndex] = RowColumn(
//         text: _sanitizeText(columns[lastIndex].text),
//         width: (columns[lastIndex].width - excess).clamp(1, 12),
//         align: columns[lastIndex].align,
//         bold: columns[lastIndex].bold,
//         textSize: columns[lastIndex].textSize,
//       );
//     }
    
//     // Convert to PosColumn format
//     final List<PosColumn> posColumns = columns.map((col) {
//       return PosColumn(
//         text: col.text,
//         width: col.width, // Use original width (already validated)
//         styles: PosStyles(
//           align: col.align,
//           bold: col.bold,
//           width: col.textSize,
//           height: col.textSize,
//         ),
//       );
//     }).toList();

//     // Apply left alignment for all printers, handle centering manually
//     _printer.rawBytes(_config.alignmentCommands[PosAlign.left]!);
    
//     if (_config.brand == PrinterBrand.star) {
//       // For STAR, add left margin by modifying first column text
//       if (posColumns.isNotEmpty) {
//         posColumns[0] = PosColumn(
//           text: ' ' * _config.leftMargin + posColumns[0].text,
//           width: posColumns[0].width,
//           styles: posColumns[0].styles,
//         );
//       }
//     }

//     _printer.row(posColumns);
//   }

//   // Enhanced image printing
//   void printImage(img.Image image, {PosAlign align = PosAlign.center}) {
//     if (_config.brand == PrinterBrand.star) {
//       // STAR needs different image handling
//       _printer.rawBytes([0x1B, 0x61, 0x01]); // Force center for images
//       _printer.feed(1); // Extra spacing before image
//     } else {
//       _printer.rawBytes(_config.alignmentCommands[align]!);
//     }

//     _printer.image(image, align: align);

//     if (_config.brand == PrinterBrand.star) {
//       _printer.feed(1); // Extra spacing after image
//     }
//   }

//   // Get max width for current printer and font size
//   int getMaxWidth(PosTextSize size) {
//     return _config.maxWidths[size] ?? 48;
//   }

//   // Enhanced text wrapping với printer-specific width
//   List<String> wrapText(String text, PosTextSize size) {
//     final maxWidth = getMaxWidth(size);
//     if (text.isEmpty) return [''];

//     final List<String> lines = [];
//     String currentLine = '';
//     final words = text.split(' ');

//     for (final String word in words) {
//       final testLine = currentLine.isEmpty ? word : '$currentLine $word';
//       if (testLine.length <= maxWidth) {
//         currentLine = testLine;
//       } else {
//         if (currentLine.isNotEmpty) {
//           lines.add(currentLine);
//           currentLine = word;
//         } else {
//           if (word.length <= maxWidth) {
//             lines.add(word);
//             currentLine = '';
//           } else {
//             int start = 0;
//             while (start < word.length) {
//               final int end = (start + maxWidth).clamp(0, word.length);
//               lines.add(word.substring(start, end));
//               start = end;
//             }
//             currentLine = '';
//           }
//         }
//       }
//     }
//     if (currentLine.isNotEmpty) lines.add(currentLine);
//     return lines.isNotEmpty ? lines : [''];
//   }

//   void disconnect() {
//     _printer.disconnect();
//   }

//   void cut() {
//     _printer.cut();
//   }
// }

// // Helper class for row columns
// class RowColumn {
//   final String text;
//   final int width;
//   final PosAlign align;
//   final bool bold;
//   final PosTextSize textSize;

//   RowColumn({
//     required this.text,
//     required this.width,
//     this.align = PosAlign.left,
//     this.bold = false,
//     this.textSize = PosTextSize.size1,
//   }) : assert(width >= 1 && width <= 12, 'Column width must be between 1 and 12');
  
//   // Copy method for modifications
//   RowColumn copyWith({
//     String? text,
//     int? width,
//     PosAlign? align,
//     bool? bold,
//     PosTextSize? textSize,
//   }) {
//     return RowColumn(
//       text: text ?? this.text,
//       width: width ?? this.width,
//       align: align ?? this.align,
//       bold: bold ?? this.bold,
//       textSize: textSize ?? this.textSize,
//     );
//   }
// }

// final Order orderTest = Order(
//     orderNumber: '123',
//     subtotal: 125.99,
//     taxTotal: 10.99,
//     total: 136.98,
//     customerName: 'Test Printer',
//     items: [
//       ItemOrder(
//         title:
//             'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli',
//         subtotal: 71,
//         quantity: 1,
//         total: 71,
//         unitPrice: 71,
//         modifiers: [
//           ModifierOrder(
//             price: 10,
//             quantity: 1,
//             taxRate: 0,
//             taxAmount: 0,
//             taxInclusive: false,
//             modifier: ModifierItem(
//                 name: 'Options', maxFree: 1, options: [], products: []),
//             option: ModifierOption(
//               name: 'Chow Mein',
//               price: 10,
//               currencyCode: 'USD',
//             ),
//           ),
//         ],
//       ),
//       ItemOrder(
//         title: 'Salad Mix with Grilled Pork/Chicken',
//         subtotal: 25,
//         quantity: 1,
//         total: 25,
//         unitPrice: 25,
//         modifiers: [
//           ModifierOrder(
//             price: 10,
//             quantity: 1,
//             taxRate: 0,
//             taxAmount: 0,
//             taxInclusive: false,
//             modifier: ModifierItem(name: 'Options', maxFree: 1, options: [], products: []),
//             option: ModifierOption(
//               name: 'Chow Mein',
//               price: 10,
//               currencyCode: 'USD',
//             ),
//           ),
//         ],
//       ),
//     ],
//   );

// // Hàm helper để sanitize text
//   String _sanitizeText(String? text) {
//     if (text == null || text.isEmpty) return '';
//     return removeDiacritics(text);
//   }

//   // Hàm helper để load ảnh từ URL hoặc assets
//   Future<Uint8List?> loadImageBytes(String imagePath) async {
//     try {
//       // Kiểm tra nếu là URL (bắt đầu với http hoặc https)
//       if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
//         // Load ảnh từ URL
//         final response = await http.get(Uri.parse(imagePath));
//         if (response.statusCode == 200) {
//           return response.bodyBytes;
//         } else {
//           print('Không thể tải ảnh từ URL: ${response.statusCode}');
//           return null;
//         }
//       } else {
//         // Load ảnh từ assets
//         final ByteData data = await rootBundle.load(imagePath);
//         return data.buffer.asUint8List();
//       }
//     } catch (e) {
//       print('Lỗi khi load ảnh: $e');
//       return null;
//     }
//   }

//   String combineAddress({
//     String? street,
//     String? city,
//     String? country,
//     String? state,
//   }) {
//     // Tạo danh sách các field không rỗng
//     final List<String> addressParts = [
//       if (street != null && street.isNotEmpty) street,
//       if (city != null && city.isNotEmpty) city,
//       if (state != null && state.isNotEmpty) state,
//       if (country != null && country.isNotEmpty) country,
//     ];

//     // Ghép các phần tử với dấu phẩy
//     return addressParts.join(', ');
//   }

// // Cách sử dụng trong hàm print chính
// Future<void> printCustomerReceiptUniversal({
//   required BuildContext context,
//   required String ipAddress,
//   required Order order,
//   String? logoPath,
//   String? printerType,
//   bool isPrintTest = false,
// }) async {
//   final handler = UniversalPrinterHandler();

//   final l10n = context.l10n;
//     final Order orderPrint = isPrintTest ? orderTest : order;
//     final List<ItemOrder> receiptItems = orderPrint.items ?? [];
//     final business = context.read<AuthBloc>().state.business;

//     final DateTime now = DateTime.now();
//     final String formattedDate = intl.DateFormat('d MMM, y').format(now);
//     final String formattedTime = intl.DateFormat('hh:mm a').format(now);
  
//   try {
//     final ReceiptTemplate? template = await AppLocalStorage.getCustomerReceipt();
//       AppLog.d('template == ${jsonEncode(template)}');
//       final isShowLogo = template?.isShowBusinessLogo ?? false;
//       final isShowBusinessName = template?.isShowBusinessName ?? false;
//       final isShowBusinessContact = template?.isShowBusinessContact ?? false;
//       final isShowCustomerInfo = template?.isShowCustomerInfo ?? false;
//       final isShowOrderInfo = template?.isShowOrderInfo ?? false;
//       final isShowOrderModifier = template?.isShowOrderModifier ?? false;
//       final isShowAdditionalText = template?.footerText?.isNotEmpty ?? false;

//       final businessLogo = logoPath ?? business?.logo ?? '';
//       final businessName = business?.name ?? '';
//       final businessStreet = business?.address?.street ?? '';
//       final businessCity = business?.address?.city ?? '';
//       final businessState = business?.address?.state ?? '';
//       final businessCountry = business?.address?.country ?? '';
//       final businessAddress = combineAddress(
//         street: businessStreet,
//         city: businessCity,
//         country: businessCountry,
//         state: businessState,
//       );
//       final businessPhone = business?.phone ?? '';
//       final customerName = orderPrint.customerName ?? orderPrint.customer?.displayName ?? '';
//       final orderNumber = orderPrint.orderNumber ?? '';
//       final totalItems = receiptItems.length;
//       final totalItemsText = totalItems > 1 ? l10n.items.toCapitalizedWords() : l10n.item.toCapitalizedWords();
//       final subTotal = orderPrint.subtotal ?? 0;
//       final subTotalText = CurrencyHelper.convertMoney(subTotal.toString(), code: orderPrint.currencyCode ?? 'USD');
//       final tax = orderPrint.taxTotal ?? 0;
//       final taxText = CurrencyHelper.convertMoney(tax.toString(), code: orderPrint.currencyCode ?? 'USD');
//       final total = orderPrint.total ?? 0;
//       final totalText = CurrencyHelper.convertMoney(total.toString(), code: orderPrint.currencyCode ?? 'USD');
//       final additionalText = template?.footerText ?? l10n.thanksYou;

//       final profile = await CapabilityProfile.load();
//       final printer = NetworkPrinter(PaperSize.mm80, profile);

//       // final connectResult = await printer.connect(ipAddress, port: 9100);
//     await handler.initialize(ipAddress);
    
//     // Print logo
//     if (isShowLogo && businessLogo.isNotEmpty) {
//       final bytes = await loadImageBytes(businessLogo);
//       if (bytes != null) {
//         final img.Image? originalImage = img.decodeImage(bytes);
//         if (originalImage != null) {
//           final logoImage = img.copyResize(
//             originalImage,
//             width: 100,
//             height: 100,
//             interpolation: img.Interpolation.linear,
//           );
//           handler.printImage(logoImage, align: PosAlign.center);
//         }
//       }
//     }

//     // Print business name
//     if (isShowBusinessName) {
//       handler.printText(
//         businessName,
//         align: PosAlign.center,
//         bold: true,
//         size: PosTextSize.size2,
//       );
//     }

//     // Print business contact
//     if (isShowBusinessContact) {
//       handler.printText(
//         '$businessAddress\n$businessPhone',
//         align: PosAlign.center,
//         size: PosTextSize.size1,
//       );
//     }

//     // Print separator
//     handler.printText(
//       '--------------------------------',
//       align: PosAlign.center,
//     );

//     // Print customer info với row format
//     if (isShowCustomerInfo) {
//       handler.printText(
//         'Customer Name',
//         align: PosAlign.left,
//         size: PosTextSize.size1,
//       );
//       handler.printText(
//         customerName,
//         align: PosAlign.left,
//         bold: true,
//         size: PosTextSize.size2,
//       );
//     }

//     // Print items
//     for (final item in receiptItems) {
//       final quantity = '${item.quantity ?? 1}';
//       final itemTitle = '$quantity x ${item.title}';
//       final price = CurrencyHelper.convertMoney(
//         (item.subtotal ?? 0).toString(),
//         code: order.currencyCode ?? 'USD',
//       );

//       // Wrap text theo printer-specific width
//       final titleLines = handler.wrapText(itemTitle, PosTextSize.size1);
      
//       // Print first line with price - đảm bảo tổng width = 12
//       handler.printRow([
//         RowColumn(
//           text: _sanitizeText(titleLines.first),
//           width: 9, // 9 + 3 = 12
//           align: PosAlign.left,
//           bold: true,
//         ),
//         RowColumn(
//           text: _sanitizeText(price),
//           width: 3, // 9 + 3 = 12
//           align: PosAlign.right,
//         ),
//       ]);

//       // Print remaining lines
//       for (int i = 1; i < titleLines.length; i++) {
//         handler.printRow([
//           RowColumn(
//             text: _sanitizeText(titleLines[i]),
//             width: 12, // Toàn bộ width cho text
//             align: PosAlign.left,
//             bold: true,
//           ),
//         ]);
//       }

//       // Print modifiers
//       if (isShowOrderModifier && item.modifiers != null) {
//         for (final modifier in item.modifiers!) {
//           final modifierText = modifier.option?.name ?? '';
//           if (modifierText.isNotEmpty) {
//             final modifierLines = handler.wrapText(modifierText, PosTextSize.size1);
//             for (final line in modifierLines) {
//               handler.printRow([
//                 RowColumn(
//                   text: '  ${_sanitizeText(line)}', // Indent modifier
//                   width: 12, // Toàn bộ width
//                   align: PosAlign.left,
//                 ),
//               ]);
//             }
//           }
//         }
//       }
//     }

//     // Print totals
//     handler.printRow([
//       RowColumn(
//         text: 'Subtotal',
//         width: 6,
//         align: PosAlign.left,
//         bold: true,
//       ),
//       RowColumn(
//         text: _sanitizeText(subTotalText),
//         width: 6,
//         align: PosAlign.right,
//       ),
//     ]);

//     handler.printRow([
//       RowColumn(
//         text: 'Tax',
//         width: 6,
//         align: PosAlign.left,
//         bold: true,
//       ),
//       RowColumn(
//         text: taxText,
//         width: 6,
//         align: PosAlign.right,
//       ),
//     ]);

//     handler.printRow([
//       RowColumn(
//         text: 'Total',
//         width: 6,
//         align: PosAlign.left,
//         bold: true,
//       ),
//       RowColumn(
//         text: totalText,
//         width: 6,
//         align: PosAlign.right,
//         bold: true,
//       ),
//     ]);

//     // Print footer
//     if (isShowAdditionalText) {
//       handler.printText(
//         '--------------------------------',
//         align: PosAlign.center,
//       );
//       handler.printText(
//         additionalText.toUpperCase(),
//         align: PosAlign.center,
//         bold: true,
//       );
//     }

//   } finally {
//     handler..cut()
//     ..disconnect();
//   }

// }

class CustomerReceiptTemplateWidget {

  final Order orderTest = Order(
    orderNumber: '123',
    subtotal: 125.99,
    taxTotal: 10.99,
    total: 136.98,
    customerName: 'Test Printer',
    items: [
      ItemOrder(
        title:
            'Charbroiled Pork, Beef or Chicken & Charbroiled Shrimp over Vermicelli',
        subtotal: 71,
        quantity: 1,
        total: 71,
        unitPrice: 71,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(
                name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
      ItemOrder(
        title: 'Salad Mix with Grilled Pork/Chicken',
        subtotal: 25,
        quantity: 1,
        total: 25,
        unitPrice: 25,
        modifiers: [
          ModifierOrder(
            price: 10,
            quantity: 1,
            taxRate: 0,
            taxAmount: 0,
            taxInclusive: false,
            modifier: ModifierItem(name: 'Options', maxFree: 1, options: [], products: []),
            option: ModifierOption(
              name: 'Chow Mein',
              price: 10,
              currencyCode: 'USD',
            ),
          ),
        ],
      ),
    ],
  );

  Future<void> printCustomerReceiptAsText({
    required BuildContext context,
    required String ipAddress,
    required Order order,
    String? logoPath,
    String? printerType,
    bool isPrintTest = false,
  }) async {
    final l10n = context.l10n;
    final Order orderPrint = isPrintTest ? orderTest : order;
    final List<ItemOrder> receiptItems = orderPrint.items ?? [];
    final business = context.read<AuthBloc>().state.business;

    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    // final DateTime now = DateTimeHelper.currentTime(timeZone);
    // final String formattedDate = intl.DateFormat('d MMM, y').format(now);
    // final String formattedTime = intl.DateFormat('hh:mm a').format(now);

    try {
      final ReceiptTemplate? template = await AppLocalStorage.getCustomerReceipt();
      AppLog.d('template == ${jsonEncode(template)}');
      final isShowLogo = template?.isShowBusinessLogo ?? false;
      final isShowBusinessName = template?.isShowBusinessName ?? false;
      final isShowBusinessContact = template?.isShowBusinessContact ?? false;
      final isShowCustomerInfo = template?.isShowCustomerInfo ?? false;
      final isShowOrderInfo = template?.isShowOrderInfo ?? false;
      final isShowOrderModifier = template?.isShowOrderModifier ?? false;
      final isShowAdditionalText = template?.footerText?.isNotEmpty ?? false;

      final businessLogo = logoPath ?? business?.logo ?? '';
      final businessName = business?.name ?? '';
      final businessStreet = business?.address?.street ?? '';
      final businessCity = business?.address?.city ?? '';
      final businessState = business?.address?.state ?? '';
      final businessCountry = business?.address?.country ?? '';
      final businessAddress = combineAddress(
        street: businessStreet,
        city: businessCity,
        country: businessCountry,
        state: businessState,
      );
      final businessPhone = business?.phone ?? '';
      final phone = AppValidations.formatPhoneNumber(businessPhone);
      final customerName = orderPrint.customerName ?? orderPrint.customer?.displayName ?? '';
      final orderNumber = orderPrint.orderNumber ?? '';
      // final totalItems = receiptItems.length;
      final totalItems = getTotalQuantity(order, isPrintTest: isPrintTest);
      final totalItemsText = totalItems > 1 ? l10n.items.toCapitalizedWords() : l10n.item.toCapitalizedWords();
      final subTotal = orderPrint.subtotal ?? 0;
      final subTotalText = CurrencyHelper.convertMoney(subTotal.toString(), code: orderPrint.currencyCode ?? 'USD');
      final tax = orderPrint.taxTotal ?? 0;
      final taxText = CurrencyHelper.convertMoney(tax.toString(), code: orderPrint.currencyCode ?? 'USD');
      final total = orderPrint.total ?? 0;
      final totalText = CurrencyHelper.convertMoney(total.toString(), code: orderPrint.currencyCode ?? 'USD');
      final orderTime = orderPrint.createdAt ?? orderPrint.updatedAt ?? DateTime.now();
      final orderTimeFormat = DateTimeHelper.dateTimeLongFormat(
        orderTime,
        timeZone: timeZone,
        isWeekDay: false,
        langLocale: Localizations.localeOf(context).languageCode,
        format: 'd MMM, y - hh:mm a',
      );
      AppLog.d('businessLogo = $businessLogo');
      
      final additionalText = template?.footerText ?? l10n.thanksYou;

      final profile = await CapabilityProfile.load();
      final printer = NetworkPrinter(PaperSize.mm80, profile);

      final connectResult = await printer.connect(ipAddress, port: 9100);
      // if (connectResult.value == 1) { //// TODO: chuyen thanh 1
        AppLog.e('connectResult receipt = ${connectResult.value} // check succss = ${connectResult == PosPrintResult.success}');
      if (connectResult == PosPrintResult.success) {
      // Text wrapping functions với dynamic width
        List<String> wrapItemName(String text, int maxWidth) {
          if (text.isEmpty) return [''];

          final List<String> lines = [];
          String currentLine = '';
          final words = text.split(' ');

          for (final String word in words) {
            final testLine = currentLine.isEmpty ? word : '$currentLine $word';
            if (testLine.length <= maxWidth) {
              currentLine = testLine;
            } else {
              if (currentLine.isNotEmpty) {
                lines.add(currentLine);
                currentLine = word;
              } else {
                // Word quá dài, cắt nó
                if (word.length <= maxWidth) {
                  lines.add(word);
                  currentLine = '';
                } else {
                  // Cắt word dài thành nhiều pieces
                  int start = 0;
                  while (start < word.length) {
                    final int end = (start + maxWidth).clamp(0, word.length);
                    lines.add(word.substring(start, end));
                    start = end;
                  }
                  currentLine = '';
                }
              }
            }
          }
          if (currentLine.isNotEmpty) lines.add(currentLine);
          return lines.isNotEmpty ? lines : [''];
        }

        List<String> wrapModifiers(String text, int maxWidth) {
          if (text.isEmpty) return [''];

          final List<String> lines = [];
          String currentLine = '';
          final words = text.split(' ');

          for (final String word in words) {
            final testLine = currentLine.isEmpty ? word : '$currentLine $word';
            if (testLine.length <= maxWidth) {
              currentLine = testLine;
            } else {
              if (currentLine.isNotEmpty) {
                lines.add(currentLine);
                currentLine = word;
              } else {
                // Word quá dài, cắt nó
                if (word.length <= maxWidth) {
                  lines.add(word);
                  currentLine = '';
                } else {
                  // Cắt word dài thành nhiều pieces
                  int start = 0;
                  while (start < word.length) {
                    final int end = (start + maxWidth).clamp(0, word.length);
                    lines.add(word.substring(start, end));
                    start = end;
                  }
                  currentLine = '';
                }
              }
            }
          }
          if (currentLine.isNotEmpty) lines.add(currentLine);
          return lines.isNotEmpty ? lines : [''];
        }
        
        // in LOGO
        if (isShowLogo && businessLogo != null) {
          try {
            // Sử dụng hàm helper để load ảnh
            final Uint8List? bytes = await loadImageBytes(businessLogo);

            if (bytes != null) {
              const logoWidth = 100;
              const logoHeight = 100;
              final img.Image? originalImage = img.decodeImage(bytes);

              if (originalImage != null) {
                img.Image logoImage = originalImage;

                // Resize ảnh nếu cần
                if (logoWidth != null || logoHeight != null) {
                  logoImage = img.copyResize(
                    originalImage,
                    width: logoHeight != null
                        ? (logoHeight *
                                originalImage.width /
                                originalImage.height)
                            .round()
                        : originalImage.width,
                    height: logoWidth != null
                        ? (logoWidth *
                                originalImage.height /
                                originalImage.width)
                            .round()
                        : originalImage.height,
                    interpolation: img.Interpolation.linear,
                  );
                }

                printer.image(logoImage, align: PosAlign.center);
              }
            } else {
              print('Không thể load ảnh logo');
            }
          } catch (e) {
            print('Lỗi in logo: $e');
            // Tiếp tục in mà không có logo
          }
        }

        // in BUSINESS NAME
        if (isShowBusinessName) {
          try {
            printer.text(
              _sanitizeText(businessName),
              styles: const PosStyles(
                align: PosAlign.center,
                bold: true,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            );
          } catch (e) {
            print('Lỗi in business name: $e');
            // Tiếp tục in mà không có business name
          }
        }

        // in BUSINESS CONTACT
        if (isShowBusinessContact) {
          try {
            // printer.text(
            //   _sanitizeText('$businessAddress\n$phone'),
            //   styles: const PosStyles(
            //     align: PosAlign.center,
            //     width: PosTextSize.size1,
            //     height: PosTextSize.size1,
            //   ),
            // );
            printWrappedText(
              printer,
              '$businessAddress\n$phone',
              maxWidth: 36, // Điều chỉnh theo font size
              styles: const PosStyles(
                align: PosAlign.center,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            );
          } catch (e) {
            print('Lỗi in business contact: $e');
            // Tiếp tục in mà không có business contact
          }
        }
        if (isShowLogo || isShowBusinessName || isShowBusinessContact) {
          printer.hr(ch: '-', linesAfter: 0);
        }
        printer.feed(1);

        // in CUSTOMER INFO
        if (isShowCustomerInfo) {
          try {
            printer
              ..rawBytes([0x1B, 0x61, 0x00])
              ..text(
                _sanitizeText(l10n.receiptCustomerName),
                styles: const PosStyles(
                  align: PosAlign.left,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              );
              printWrappedText(
              printer,
              _sanitizeText(customerName),
              maxWidth: 36, // Điều chỉnh theo font size
              styles: const PosStyles(
                align: PosAlign.left,
                bold: true,
                width: PosTextSize.size2,
                height: PosTextSize.size2,
              ),
            );
              // ..text(
              //   // _sanitizeText(customerName),
              //   styles: const PosStyles(
              //     align: PosAlign.left,
              //     bold: true,
              //     width: PosTextSize.size2,
              //     height: PosTextSize.size2,
              //   ),
              // )
              printer.row(
                [
                  PosColumn(
                    text: '',
                    width: 12,
                    styles: const PosStyles(
                      width: PosTextSize.size1,
                      height: PosTextSize.size1,
                    ),
                  ),
                ],
              );
          } catch (e) {
            print('Lỗi in customer name: $e');
            // Tiếp tục in mà không có customer name
          }
        }

        // in ORDER INFO
        if (isShowOrderInfo) {
          try {
            printer
              ..text(
                _sanitizeText(l10n.orderTime),
                styles: const PosStyles(
                  align: PosAlign.left,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              )
              ..text(
                // _sanitizeText('$formattedDate, $formattedTime'),
                // _sanitizeText('$orderTimeFormatDate, $orderTimeFormatTime'),
                _sanitizeText(orderTimeFormat),
                styles: const PosStyles(
                  align: PosAlign.left,
                  bold: true,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              )
              ..text(
                _sanitizeText(l10n.receiptOrderNumber),
                styles: const PosStyles(
                  align: PosAlign.left,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              )
              ..text(
                _sanitizeText('#$orderNumber'),
                styles: const PosStyles(
                  align: PosAlign.left,
                  bold: true,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              )
              ..text(
                _sanitizeText(l10n.total),
                styles: const PosStyles(
                  align: PosAlign.left,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              )
              ..text(
                _sanitizeText('$totalItems $totalItemsText'),
                styles: const PosStyles(
                  align: PosAlign.left,
                  bold: true,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              );
            if (isShowCustomerInfo || isShowOrderInfo) {
              printer.hr(ch: '-', linesAfter: 0);
            }

            // printer.feed(1);
          } catch (e) {
            print('Lỗi in pickup time: $e');
            // Tiếp tục in mà không có pickup time
          }
        }

        // in ITEMS

        int getMaxWidthForFontSize(PosTextSize fontSize) {
          switch (fontSize) {
            case PosTextSize.size1:
              return 36; // Giữ nguyên
            case PosTextSize.size2:
              return 20; // Giảm từ 28 xuống 18 (chia ~2)
            case PosTextSize.size3:
              return 14; // Giảm từ 20 xuống 12 (chia ~3)
            case PosTextSize.size4:
              return 9; // Giảm từ 16 xuống 9 (chia ~4)
            default:
              return 36;
          }
        }

        int getMaxWidthForModifier(PosTextSize fontSize) {
          switch (fontSize) {
            case PosTextSize.size1:
              return 36;
            case PosTextSize.size2:
              return 19;
            case PosTextSize.size3:
              return 14;
            case PosTextSize.size4:
              return 9;
            default:
              return 36;
          }
        }

        // In danh sách items với dynamic width handling
        for (int index = 0; index < receiptItems.length; index++) {
          final item = receiptItems[index];
          final subTotal = item.subtotal ?? 0;
          final String fullPrice = CurrencyHelper.convertMoney(
              subTotal.toString(),
              code: orderPrint.currencyCode ?? 'USD');
          // final String fullPrice = intl.NumberFormat('#,##0', 'en_US').format(subTotal);

          try {
            // Safe text processing
            final String quantity = '${item.quantity ?? 1}';
            final String itemTitle = _sanitizeText('$quantity  x  ${item.title}');

            if (itemTitle.isEmpty) {
              continue; // Skip items with empty title
            }

            // Tính toán width dựa trên font size
            final int maxWidthForTitle =
                getMaxWidthForFontSize(PosTextSize.size1);
            final int maxWidthForModifier =
                getMaxWidthForModifier(PosTextSize.size1);

            final titleLines = wrapItemName(itemTitle, maxWidthForTitle);

            // In dòng đầu tiên với quantity
            printer
              ..rawBytes([0x1B, 0x61, 0x00])
              ..row([
                PosColumn(
                  text: titleLines.isNotEmpty ? titleLines.first : itemTitle,
                  // text: '$quantity  x  ${titleLines.isNotEmpty ? titleLines.first : itemTitle}',
                  width: 10, // Tăng width cho quantity column
                  styles: const PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
                PosColumn(
                  text: fullPrice,
                  width: 2, // Giảm width cho title column để balance
                  styles: const PosStyles(
                    align: PosAlign.right,
                    // bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
              ]);

            // In TẤT CẢ các dòng tiếp theo của title (không bị limit)
            for (int i = 1; i < titleLines.length; i++) {
              printer.row([
                PosColumn(
                  text: titleLines[i],
                  width: 10,
                  styles: const PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
                PosColumn(
                  text: '',
                  width: 2,
                  styles: const PosStyles(
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
              ]);
            }
            // printer.feed(1);

            // In TẤT CẢ modifiers với error handling
            if (isShowOrderModifier) {
              if (item.modifiers != null && item.modifiers!.isNotEmpty) {
                for (final modifier in item.modifiers!) {
                  try {
                    final String modifierText = _sanitizeText(modifier.option?.name);
                    if (modifierText.isNotEmpty) {
                      final modifierLines =
                          wrapModifiers(modifierText, maxWidthForModifier);

                      // In TỪNG DÒNG của modifier giống như ProductName
                      for (final line in modifierLines) {
                        if (line.isNotEmpty) {
                          printer.row([
                            PosColumn(
                              text: line, // MỖI dòng riêng biệt
                              width: 10,
                              styles: const PosStyles(
                                align: PosAlign.left,
                                width: PosTextSize.size1,
                                height: PosTextSize.size1,
                              ),
                            ),
                            PosColumn(
                              text: '', // Empty space để canh lề
                              width: 2,
                              styles: const PosStyles(
                                width: PosTextSize.size1,
                                height: PosTextSize.size1,
                              ),
                            ),
                          ]);
                        }
                      }
                    }
                  } catch (e) {
                    print('Lỗi in modifier cho item $index: $e');
                  }
                }
              }
            }

            // Add separator after each item
            printer
              ..hr(ch: '_', linesAfter: 0)
              ..feed(1);
          } catch (e) {
            print('Lỗi in item $index: $e');
            // In một dòng backup
            try {} catch (backupError) {
              print('Lỗi in backup text: $backupError');
            }
          }
        }

        // in SUBTOTAL
        try {
          printer
            ..row(
              [
                PosColumn(
                  text: _sanitizeText(l10n.subtotal),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
                PosColumn(
                  text: _sanitizeText(subTotalText),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.right,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
              ],
            )
            ..row(
              [
                PosColumn(
                  text: _sanitizeText(l10n.tax),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
                PosColumn(
                  text: _sanitizeText(taxText),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.right,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
              ],
            )
            ..row(
              [
                PosColumn(
                  text: _sanitizeText(l10n.total),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.left,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
                PosColumn(
                  text: _sanitizeText(totalText),
                  width: 6,
                  styles: const PosStyles(
                    align: PosAlign.right,
                    bold: true,
                    width: PosTextSize.size1,
                    height: PosTextSize.size1,
                  ),
                ),
              ],
            )
            ..feed(1);
        } catch (e) {
          print('Lỗi in subtotal: $e');
          // Tiếp tục in mà không có subtotal
        }

        // in Footer
        if (isShowAdditionalText) {
          try {
            printer
              ..rawBytes([0x1B, 0x61, 0x01])
              ..hr(ch: '-', linesAfter: 0)
              ..feed(1)
              ..text(
                _sanitizeText(additionalText.toUpperCase()),
                styles: const PosStyles(
                  align: PosAlign.center,
                  bold: true,
                ),
              );
          } catch (e) {
            print('Lỗi in footer: $e');
            printer.cut(); // Ít nhất cũng cắt giấy
          }
        }
        // finally {
        printer
          ..cut()
          ..disconnect();
        // }
        // } else {
        print('Không thể kết nối máy in');
      }
    } catch (e) {
      print('Lỗi in hóa đơn: $e');
      rethrow; // Re-throw để caller có thể handle
    }
  }

  void setCenterAlignment(String printerType, NetworkPrinter printer) {
    if (printerType.toUpperCase() == 'STAR') {
      // STAR printers
      printer.rawBytes([0x1B, 0x1D, 0x61, 0x01]); // STAR center command
    } else {
      // EPSON printers (default)
      printer.rawBytes([0x1B, 0x61, 0x01]); // EPSON center command
    }
  }

  void setLeftAlignment(String printerType, NetworkPrinter printer) {
    if (printerType.toUpperCase() == 'STAR') {
      printer.rawBytes([0x1B, 0x1D, 0x61, 0x00]); // STAR left
    } else {
      printer.rawBytes([0x1B, 0x61, 0x00]); // EPSON left
    }
  }

  String centerText({required String text, int totalWidth = 36}) {
    if (text.length >= totalWidth) return text;
    final int padding = (totalWidth - text.length) ~/ 2;
    return ' ' * padding + text;
  }

  // Hàm helper để sanitize text
  String _sanitizeText(String? text) {
    if (text == null || text.isEmpty) return '';
    return removeDiacritics(text);
  }

  // Hàm helper để load ảnh từ URL hoặc assets
  Future<Uint8List?> loadImageBytes(String imagePath) async {
    try {
      // Kiểm tra nếu là URL (bắt đầu với http hoặc https)
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        // Load ảnh từ URL
        final response = await http.get(Uri.parse(imagePath));
        if (response.statusCode == 200) {
          return response.bodyBytes;
        } else {
          print('Không thể tải ảnh từ URL: ${response.statusCode}');
          return null;
        }
      } else {
        // Load ảnh từ assets
        final ByteData data = await rootBundle.load(imagePath);
        return data.buffer.asUint8List();
      }
    } catch (e) {
      print('Lỗi khi load ảnh: $e');
      return null;
    }
  }

  String combineAddress({
    String? street,
    String? city,
    String? country,
    String? state,
  }) {
    // Tạo danh sách các field không rỗng
    final List<String> addressParts = [
      if (street != null && street.isNotEmpty) street,
      if (city != null && city.isNotEmpty) city,
      if (state != null && state.isNotEmpty) state,
      if (country != null && country.isNotEmpty) country,
    ];

    // Ghép các phần tử với dấu phẩy
    return addressParts.join(', ');
  }

  // Helper function để wrap text cho printer.text()
  void printWrappedText(
    NetworkPrinter printer,
    String text, {
    int maxWidth = 36, // Default cho paper 80mm
    PosStyles? styles,
  }) {
    if (text.isEmpty) return;
    
    final wrappedLines = wrapText(text, maxWidth);
    
    for (final line in wrappedLines) {
      if (line.isNotEmpty) {
        printer.text(line, styles: styles ?? const PosStyles());
      }
    }
  }

  // Hàm wrap text đơn giản
  List<String> wrapText(String text, int maxWidth) {
    if (text.isEmpty) return [''];
    
    final List<String> lines = [];
    String currentLine = '';
    final words = text.split(' ');

    for (final String word in words) {
      final testLine = currentLine.isEmpty ? word : '$currentLine $word';
      if (testLine.length <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine.isNotEmpty) {
          lines.add(currentLine);
          currentLine = word;
        } else {
          // Word quá dài, cắt nó
          if (word.length <= maxWidth) {
            lines.add(word);
            currentLine = '';
          } else {
            int start = 0;
            while (start < word.length) {
              final int end = (start + maxWidth).clamp(0, word.length);
              lines.add(word.substring(start, end));
              start = end;
            }
            currentLine = '';
          }
        }
      }
    }
    if (currentLine.isNotEmpty) lines.add(currentLine);
    return lines.isNotEmpty ? lines : [''];
  }

  int getTotalQuantity(
    Order order, {
    bool isPrintTest = false,
  }) {
    if (isPrintTest) return 2;
    final items = order.items ?? [];
    return items.fold(0, (sum, item) => sum + (item.quantity ?? 0));
  }

}
