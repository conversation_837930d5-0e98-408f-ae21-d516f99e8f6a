import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customize-kitchen-ticket/customize-kitchen-ticket.cubit.dart';
import 'package:stickyqrbusiness/@widgets/custom-loading.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-kitchen-ticket-preview.dart';

class CustomizeKitchenTicketPage extends StatefulWidget {
  const CustomizeKitchenTicketPage({super.key});

  @override
  State<CustomizeKitchenTicketPage> createState() =>
      _CustomizeKitchenTicketPageState();
}

class _CustomizeKitchenTicketPageState extends State<CustomizeKitchenTicketPage>
    with WidgetsBindingObserver {
  late final l10n = context.l10n;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.sizeOf(context).width >= 600;
    return BlocListener<CustomizeKitchenTicketCubit,
        CustomizeKitchenTicketState>(
      listener: (context, state) {
        if (state.status == CustomizeKitchenTicketStatus.Updated) {
          AppBased.toastSuccess(context, title: l10n.successfully, duration: 1);
          context.read<CustomizeKitchenTicketCubit>().onResetStatus();
        }
        if (state.status == CustomizeKitchenTicketStatus.Success) {
          if (state.receiptTemplate != null &&
              state.receiptTemplate?.kitchenReceiptFontSize != null) {
            context.read<CustomizeKitchenTicketCubit>().onChangeFontSize(
                state.receiptTemplate?.kitchenReceiptFontSize ?? 1);
          }
        }
      },
      child: SafeArea(
        right: false,
        left: false,
        child: Scaffold(
          body: BlocBuilder<CustomizeKitchenTicketCubit, CustomizeKitchenTicketState>(
            builder: (context, state) {
              switch (state.status) {
                case CustomizeKitchenTicketStatus.Loading:
                  return _buildLoading();

                case CustomizeKitchenTicketStatus.Success:
                case CustomizeKitchenTicketStatus.Edit:
                case CustomizeKitchenTicketStatus.Inprogress:
                case CustomizeKitchenTicketStatus.Updated:
                  if (state.receiptTemplate != null) {
                    return isTablet
                        ? _buildContentTablet()
                        : _buildContentMobile();
                  }
                  return Center(
                    child: Text(
                      l10n.noReceiptFound,
                      style: const TextStyle(fontSize: 16),
                    ),
                  );

                case CustomizeKitchenTicketStatus.Error:
                  return _buildErrorWidget(errMsg: state.errorMsg);

                default:
                  return const SizedBox.shrink();
              }
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContentTablet() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: SafeArea(
              child: _buildSelectFontSize(),
            ),
          ),
        ),
        Container(
            decoration: const BoxDecoration(
              border: Border(
                top: BorderSide(
                  width: 1,
                color: AppColors.appBorderColor,
              ),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Align(
            alignment: Alignment.centerRight,
            child: BlocBuilder<CustomizeKitchenTicketCubit,
                CustomizeKitchenTicketState>(
              builder: (context, state) {
                return IntrinsicWidth(
                  child: ButtonLoading(
                    callback: () {
                      context.read<CustomizeKitchenTicketCubit>().onUpdateReceiptTemplate();
                    },
                    isLoading: state.status == CustomizeKitchenTicketStatus.Inprogress ? true : false,
                    label: l10n.save,
                    labelColor: Colors.white,
                    buttonBackgroundColor: const Color(0xFF0B0B0B),
                    height: 48,
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContentMobile() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: _buildSelectFontSize(),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 1,
                child: ButtonControlWidget(
                  onPressed: () {
                    _showPreview(context);
                  },
                  buttonText: l10n.preview,
                  buttonTextColor: Colors.black,
                  height: 48,
                  borderSide: const BorderSide(
                    width: 1,
                    color: Color(0xFF999CA0),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              BlocBuilder<CustomizeKitchenTicketCubit, CustomizeKitchenTicketState>(
                builder: (context, state) {
                  return Expanded(
                    flex: 1,
                    child: ButtonLoading(
                      callback: () {
                        context
                            .read<CustomizeKitchenTicketCubit>()
                            .onUpdateReceiptTemplate();
                      },
                      isLoading:
                          state.status == CustomizeKitchenTicketStatus.Inprogress
                              ? true
                              : false,
                      label: l10n.save,
                      labelColor: Colors.white,
                      buttonBackgroundColor: const Color(0xFF0B0B0B),
                      height: 48,
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSelectFontSize() {
    final List<int> listFontSize = [0, 1, 2];
    return BlocBuilder<CustomizeKitchenTicketCubit,
        CustomizeKitchenTicketState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 16),
              Text(
                l10n.chooseTicketFontSize,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black,
                ),
              ),
              const SizedBox(height: 8),
              DecoratedBox(
                decoration: BoxDecoration(
                  border: Border.all(width: 1, color: AppColors.appBorderColor),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: ListView.builder(
                    padding: EdgeInsets.zero,
                    itemCount: listFontSize.length,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (BuildContext context, int index) {
                      final int item = listFontSize[index];
                      
                      return Material(
                        color: Colors.transparent,
                        child: Theme(
                          data: Theme.of(context).copyWith(
                            hoverColor: Colors.transparent,
                            splashColor: Colors.grey.withValues(alpha: 0.1),
                            highlightColor: Colors.grey.withValues(alpha: 0.05),
                          ),
                          child: Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4),
                                child: InkWell(
                                  onTap: () {
                                    context.read<CustomizeKitchenTicketCubit>().onChangeFontSize(item);
                                  },
                                  child: Row(
                                    children: [
                                      Radio<int>(
                                        value: item,
                                        groupValue: state.fontSize,
                                        onChanged: (int? value) {
                                          if (value != null) {
                                            context.read<CustomizeKitchenTicketCubit>().onChangeFontSize(value);
                                          }
                                        },
                                        activeColor: Colors.black,
                                      ),
                                      Expanded(
                                        child: Text(
                                          _getFontSizeName(item),
                                          style: const TextStyle(
                                            fontSize: 16,
                                            color: Colors.black,
                                            fontWeight: FontWeight.normal,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (index < listFontSize.length - 1)
                                const Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 20),
                                  child: Divider(
                                    thickness: 0.7,
                                    color: Color(0xFFD5D9DC),
                                    height: 1,
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoading() {
    final width = (MediaQuery.of(context).size.width) * 0.6 - 70;
    final item = Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ItemLoadingCustomWidget(width: width * .6, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
        const SizedBox(height: 48),
        ItemLoadingCustomWidget(width: width * .45, height: 12),
        const SizedBox(height: 16),
        ItemLoadingCustomWidget(width: width * .55, height: 12),
      ],
    );
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 24),
      children: <Widget>[
        LoadingCustomWidget(
          lineItem: 1,
          isBorder: false,
          isPadding: true,
          paddingList: EdgeInsets.zero,
          paddingItem: const EdgeInsets.only(bottom: 8),
          widget: item,
        ),
      ],
    );
  }

  Widget _buildErrorWidget({String? errMsg}) {
    return Center(
      child: Text(
        errMsg ?? l10n.somethingWentWrong,
        style: const TextStyle(fontSize: 16),
      ),
    );
  }

  String _getFontSizeName(int fontSize) {
    switch (fontSize) {
      case 0:
        return l10n.kitchenTicketSmall;
      case 1:
        return l10n.kitchenTicketDefault;
      case 2:
        return l10n.kitchenTicketLarge;
      default:
        return l10n.kitchenTicketDefault;
    }
  }

  Future<void> _showPreview(BuildContext context) async {
    await showGeneralDialog(
      barrierLabel: '',
      barrierDismissible: true,
      barrierColor: Colors.black.withValues(alpha: 0.4),
      context: context,
      pageBuilder: (context, anim1, anim2) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Container(
            alignment: Alignment.center,
            padding: MediaQuery.of(context).viewInsets,
            child:
                const CustomizeKitchenTicketPreviewWidget(isShowAppbar: true),
          ),
        );
      },
      transitionBuilder: (context, anim1, anim2, child) {
        return SlideTransition(
          position:
              Tween(begin: const Offset(0, 1), end: Offset.zero).animate(anim1),
          child: child,
        );
      },
    );
  }

  void _buildCompleted() {
    context.read<CustomizeKitchenTicketCubit>().getReceiptTemplates();
  }
}
