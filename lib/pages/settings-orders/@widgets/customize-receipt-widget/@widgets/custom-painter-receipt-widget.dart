
// // ignore_for_file: cascade_invocations

// import 'dart:ui' as ui;

// import 'package:esc_pos_printer_lts/esc_pos_printer_lts.dart';
// import 'package:esc_pos_utils_lts/esc_pos_utils_lts.dart';
// import 'package:flutter/material.dart';
// import 'package:image/image.dart' as img;
// import 'package:stickyqrbusiness/pages/settings-orders/@widgets/receipt-widget.dart';

// class ReceiptPainter extends CustomPainter {
//   final List<ReceiptItem> items;
//   final String date;
//   final String staffName;
//   final ui.Image? logo;
//   final String qrData;
  
//     // Các thông số cố định cho máy in nhiệt
//   final double printerWidth = 576; // Chiều rộng chuẩn cho máy in 80mm
//   final double scale = 1.0; // Tỷ lệ scale nếu cần

//   // <PERSON><PERSON> cho font để tránh load lại nhiều lần
//   late ui.Paint _paint;
//   late ui.ParagraphBuilder _builder;
//   late ui.ParagraphStyle _headerStyle;
//   late ui.ParagraphStyle _normalStyle;
  
//   ReceiptPainter({
//     required this.items,
//     required this.date,
//     required this.staffName,
//     this.logo,
//     required this.qrData,
//   }) {
//     _paint = ui.Paint()..color = Colors.black;
    
//     // Điều chỉnh font size cho phù hợp với máy in
//     _headerStyle = ui.ParagraphStyle(
//       fontSize: 24 * scale,
//       fontWeight: FontWeight.bold,
//       fontFamily: 'Noto Serif', // Hỗ trợ tiếng Việt
//       textAlign: TextAlign.center,
//     );
    
//     _normalStyle = ui.ParagraphStyle(
//       fontSize: 16 * scale,
//       fontFamily: 'Roboto Mono', // Hỗ trợ tiếng Việt
//       textAlign: TextAlign.left,
//     );
//   }

//   @override
//   void paint(Canvas canvas, Size size) {
//     double currentY = 0;
    
//     // Vẽ logo nếu có
//     if (logo != null) {
//       canvas.drawImage(logo!, Offset(125, currentY), _paint);
//       currentY += 120; // Logo height + padding
//     }
    
//     // Vẽ tiêu đề
//     _builder = ui.ParagraphBuilder(_headerStyle)
//       ..pushStyle(ui.TextStyle(color: Colors.black))
//       ..addText('HÓA ĐƠN BÁN HÀNG');
//     final paragraph = _builder.build();
//     paragraph.layout(ui.ParagraphConstraints(width: size.width));
//     canvas.drawParagraph(paragraph, Offset(0, currentY));
//     currentY += paragraph.height + 20;
    
//     // Vẽ thông tin ngày và nhân viên
//     _drawInfoLine(canvas, 'Ngày bán:', date, currentY, size);
//     currentY += 30;
//     _drawInfoLine(canvas, 'Nhân viên:', staffName, currentY, size);
//     currentY += 40;
    
//     // Vẽ đường gạch ngang
//     _drawDashedLine(canvas, currentY, size.width);
//     currentY += 20;
    
//     // Vẽ danh sách sản phẩm
//     for (final item in items) {
//       _drawItem(canvas, item, currentY, size);
//       currentY += 30;
//     }
    
//     // Vẽ QR code (nếu cần)
//     // Bạn có thể sử dụng package qr_flutter để tạo QR code image
//     // và vẽ nó bằng canvas.drawImage()
//   }
  
//   void _drawInfoLine(Canvas canvas, String label, String value, double y, Size size) {
//     // Vẽ label
//     _builder = ui.ParagraphBuilder(_normalStyle)
//       ..pushStyle(ui.TextStyle(color: Colors.black))
//       ..addText(label);
//     final labelPara = _builder.build();
//     labelPara.layout(ui.ParagraphConstraints(width: size.width / 2));
//     canvas.drawParagraph(labelPara, Offset(0, y));
    
//     // Vẽ value
//     _builder = ui.ParagraphBuilder(_normalStyle)
//       ..pushStyle(ui.TextStyle(color: Colors.black))
//       ..addText(value);
//     final valuePara = _builder.build();
//     valuePara.layout(ui.ParagraphConstraints(width: size.width / 2));
//     canvas.drawParagraph(valuePara, Offset(size.width / 2, y));
//   }
  
//   void _drawDashedLine(Canvas canvas, double y, double width) {
//     final paint = Paint()
//       ..color = Colors.black
//       ..strokeWidth = 1;
    
//     double currentX = 0;
//     while (currentX < width) {
//       canvas.drawLine(
//         Offset(currentX, y),
//         Offset(currentX + 10, y),
//         paint,
//       );
//       currentX += 15;
//     }
//   }
  
//   void _drawItem(Canvas canvas, ReceiptItem item, double y, Size size) {
//     _builder = ui.ParagraphBuilder(_normalStyle)
//       ..pushStyle(ui.TextStyle(color: Colors.black))
//       ..addText(item.name);
//     final namePara = _builder.build();
//     namePara.layout(ui.ParagraphConstraints(width: size.width * 0.7));
//     canvas.drawParagraph(namePara, Offset(0, y));
    
//     _builder = ui.ParagraphBuilder(_normalStyle)
//       ..pushStyle(ui.TextStyle(
//         color: Colors.black,
//         fontWeight: FontWeight.bold,
//       ))
//       ..addText('${item.price}đ');
//     final pricePara = _builder.build();
//     pricePara.layout(ui.ParagraphConstraints(width: size.width * 0.3));
//     canvas.drawParagraph(pricePara, Offset(size.width * 0.7, y));
//   }

//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
// }

// // Hàm để tạo và in hóa đơn
// Future<void> printReceiptYYY(
//   BuildContext context, {
//   required List<ReceiptItem> items,
//   required String date,
//   required String staffName,
//   required String qrData,
//   ui.Image? logo,
// }) async {
//   try {
//     // 1. Tạo recorder để vẽ
//     final recorder = ui.PictureRecorder();
//     final canvas = Canvas(recorder);
    
//     // 2. Tính toán chiều cao cần thiết
//     final double totalHeight = calculateTotalHeight(items);
    
//     // 3. Vẽ nội dung hóa đơn
//     final receiptPainter = ReceiptPainter(
//       items: items,
//       date: date,
//       staffName: staffName,
//       logo: logo,
//       qrData: qrData,
//     );
    
//     receiptPainter.paint(canvas, Size(576, totalHeight));
    
//     // 4. Chuyển đổi thành hình ảnh
//     final picture = recorder.endRecording();
//     final image = await picture.toImage(576, totalHeight.round());
//     final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
//     if (byteData == null) throw Exception('Không thể tạo dữ liệu hình ảnh');
    
//     // 5. In hóa đơn
//     final profile = await CapabilityProfile.load();
//     final printer = NetworkPrinter(PaperSize.mm80, profile);
    
//     final PosPrintResult res = await printer.connect('*************', port: 9100);
    
//     if (res == PosPrintResult.success) {
//       // Chuyển đổi ảnh sang grayscale và tối ưu cho máy in
//       final imageBytes = byteData.buffer.asUint8List();
//       final img.Image receiptImage = img.decodeImage(imageBytes)!;
//       final img.Image grayscaleImage = img.grayscale(receiptImage);
      
//       // In hình ảnh
//       printer.image(grayscaleImage);
      
//       printer
//         ..feed(2)
//         ..cut()
//         ..disconnect();
//     } else {
//       throw Exception('Không thể kết nối máy in');
//     }
    
//   } catch (e) {
//     print('Lỗi in hóa đơn: $e');
//     // Xử lý lỗi tùy theo nhu cầu
//   }
// }

// // Hàm tính toán chiều cao
// double calculateTotalHeight(List<ReceiptItem> items) {
//   double height = 0;
  
//   // Header
//   height += 50; // Tiêu đề
//   height += 60; // Ngày và nhân viên
//   height += 20; // Đường gạch
  
//   // Items
//   height += items.length * 30;
  
//   // Footer
//   height += 120; // QR code
//   height += 40; // Text cảm ơn
//   height += 20; // Padding cuối
  
//   return height;
// }
