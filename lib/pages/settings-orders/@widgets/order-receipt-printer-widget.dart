// import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:stickyqrbusiness/@core/store/customize-receipt/customize-receipt.cubit.dart';
import 'package:stickyqrbusiness/@utils/utils.dart';
import 'package:stickyqrbusiness/@widgets/widgets.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/capture-receipt-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-kitchen-ticket-preview.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-kitchen-ticket.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-receipt-preview.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customize-receipt.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/optimize-print-img.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/receipt-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/star-print-widget.dart';

class SettingOrderReceiptPrinterPage extends StatelessWidget {
  const SettingOrderReceiptPrinterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => CustomizeReceiptCubit(),
        ),
      ],
      child: const SettingOrderReceiptPrinterView(),
    );
  }
}

class SettingOrderReceiptPrinterView extends StatefulWidget {
  const SettingOrderReceiptPrinterView({super.key});

  @override
  State<SettingOrderReceiptPrinterView> createState() =>
      _SettingOrderReceiptPrinterViewState();
}

class _SettingOrderReceiptPrinterViewState
    extends State<SettingOrderReceiptPrinterView>
    with WidgetsBindingObserver, SingleTickerProviderStateMixin {
  late TabController _tabController;
  late final l10n = context.l10n;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => _buildCompleted());
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    final isTablet = MediaQuery.sizeOf(context).width >= 600;
    return isTablet ? _buildContentTablet() : _buildContentMobile();
  }

  Widget _buildContentMobile() {
    return Scaffold(
      appBar: _buildAppbar(),
      body: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // OrderPrinterWidget(),
          const CustomizeReceiptCustomerWidget(),
          _buildCustomizeReceiptsTab(),
          const CustomizeKitchenTicketPage(),
        ],
      ),
    );
  }

  Widget _buildContentTablet() {
    return Scaffold(
      appBar: _buildAppbar(isShowBottom: false),
      body: Row(
        children: [
          Expanded(
            flex: 4,
            child: Column(
              children: [
                _buildTabsHeader(),
                const SizedBox(height: 8),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      // OrderPrinterWidget(),
                      const CustomizeReceiptCustomerWidget(),
                      _buildCustomizeReceiptsTab(),
                      const CustomizeKitchenTicketPage(),
                    ],
                  ),
                ),
              ],
            ),
          ),
          BlocBuilder<CustomizeReceiptCubit, CustomizeReceiptState>(
            builder: (context, state) {
              final tabActive = state.tabActive;
              return Expanded(
                flex: 3,
                child: tabActive == ReceiptTab.Customer
                    ? const CustomizeReceiptPreviewWidget()
                    : const CustomizeKitchenTicketPreviewWidget(),
              );
            },
          ),
        ],
      ),
    );
  }
//   Widget _buildContentTablet() {
//   return Scaffold(
//     appBar: _buildAppbar(isShowBottom: false),
//     body: Row(
//       children: [
//         Expanded(
//           flex: 4,
//           child: Column(
//             children: [
//               _buildTabsHeader(),
//               const SizedBox(height: 8),
//               Expanded(
//                 child: TabBarView(
//                   controller: _tabController,
//                   physics: const NeverScrollableScrollPhysics(),
//                   children: [
//                     const CustomizeReceiptCustomerWidget(),
//                     _buildCustomizeReceiptsTab(),
//                     const CustomizeKitchenTicketPage(),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//         Expanded(
//           flex: 3,
//           child: AnimatedBuilder(
//             animation: _tabController,
//             builder: (context, child) {
//               return _tabController.index == 0
//                   ? CustomizeReceiptPreviewWidget()
//                   : CustomizeKitchenTicketPreviewWidget();
//             },
//           ),
//         ),
//       ],
//     ),
//   );
// }

  AppBar _buildAppbar({bool isShowBottom = true}) {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new),
        onPressed: () async {
          await Navigator.maybePop(context);
        },
      ),
      centerTitle: true,
      title: Text(
        l10n.printersReceipt,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      bottom: isShowBottom ? _buildTabsHeader() : null,
      surfaceTintColor: Colors.transparent,
      elevation: 0,
      shape: Border(
        bottom: BorderSide(
          color: Colors.grey.shade300,
          width: 1.0,
        ),
      ),
    );
  }

  PreferredSizeWidget _buildTabsHeader() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(48),
      child: DecoratedBox(
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.transparent,
            ),
          ),
        ),
        child: BlocSelector<CustomizeReceiptCubit,
            CustomizeReceiptState, ReceiptTab>(
          selector: (state) {
            return state.tabActive;
          },
          builder: (context, state) {
            return TabBar(
              controller: _tabController,
              onTap: (value) {
                context.read<CustomizeReceiptCubit>().onSelectedTab(
                    value == 0
                        ? ReceiptTab.Customer
                        : ReceiptTab.Kitchen);
              },
              labelColor: Colors.black,
              indicatorColor: Colors.black,
              dividerColor: Colors.transparent,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              // physics: const NeverScrollableScrollPhysics(),
              tabs: [
                Tab(
                  child: _buildTabTitle(
                    l10n.printers,
                    state == ReceiptTab.Customer
                        ? Colors.black
                        : Colors.black54,
                  ),
                ),
                Tab(
                  child: _buildTabTitle(
                    l10n.customizeReceipts,
                    state == ReceiptTab.Kitchen
                        ? Colors.black
                        : Colors.black54,
                  ),
                ),
                Tab(
                  child: _buildTabTitle(
                    'Customize Kitchen Ticket',
                    state == ReceiptTab.Kitchen
                        ? Colors.black
                        : Colors.black54,
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildCustomizeReceiptsTab() {
    return ListView(
      // crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 32),
        const Text('Customize Receipts Content'),
        const SizedBox(height: 32),
        ButtonLoading(
          callback: () {
            print('goi print');
            exampleUsage(context);
          },
          label: 'Test print receipt',
          buttonBackgroundColor: Colors.grey,
          labelColor: Colors.black,
        ),
        const SizedBox(height: 32),
        ButtonLoading(
          callback: () {
            AppLog.e('print star');
            print('goi print STAR');
            onPrintStar(context);
          },
          label: 'print STAR device 111',
          buttonBackgroundColor: Colors.grey,
          labelColor: Colors.black,
        ),
        const SizedBox(height: 32),
        ButtonLoading(
          callback: () {
            print('goi print optimized');
            onPrintOptimized(context);
          },
          label: 'optimize print image',
          buttonBackgroundColor: Colors.grey,
          labelColor: Colors.black,
        ),
        const SizedBox(height: 32),
      ],
    );
  }

  // InvoiceData _createTestInvoiceData() {
  //   return InvoiceData(
  //     storeName: 'CỬA HÀNG TIỆN LỢI ABC',
  //     storeAddress: '123 Đường ABC, Quận 1, TP.HCM',
  //     storePhone: 'Tel: 0901234567',
  //     invoiceNumber: 'HD${DateTime.now().millisecondsSinceEpoch}',
  //     dateTime: DateTime.now(),
  //     items: [
  //       InvoiceItem(
  //         name: 'Coca Cola 330ml',
  //         code: 'CC330',
  //         price: 15000,
  //         quantity: 2,
  //         total: 30000,
  //       ),
  //       InvoiceItem(
  //         name: 'Bánh mì sandwich',
  //         code: 'BMS01',
  //         price: 25000,
  //         quantity: 1,
  //         total: 25000,
  //       ),
  //       InvoiceItem(
  //         name: 'Nước suối Lavie 500ml',
  //         code: 'LV500',
  //         price: 8000,
  //         quantity: 3,
  //         total: 24000,
  //       ),
  //     ],
  //     totalAmount: 79000,
  //     paidAmount: 100000,
  //     changeAmount: 21000,
  //     qrCode: 'https://example.com/receipt/123456',
  //     logoImage: null, // Bạn có thể thêm logo nếu muốn
  //   );
  // }

  Future<void> exampleUsage(BuildContext context) async {
    final ReceiptPrinter receiptPrinter = ReceiptPrinter();

    /// In bằng hình ảnh
    // // Tạo widget hoá đơn -> capture -> print receipt
    final Widget receiptWidget = CaptureReceiptPrinter().buildReceiptWidget(
      customerName: 'Nguyễn Văn A',
      items: [
        ReceiptItem(
            name: 'Chả cá thác lác um meng nhiều chả ít meng',
            price: 165000.00),
        ReceiptItem(name: 'Đậu khuôn chiên', price: 40000.00),
        ReceiptItem(name: 'Bún đậu mắm tôm', price: 60.000),
        ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900.000),
        ReceiptItem(name: 'Bún chả Hà Nội', price: 200.000),
        ReceiptItem(name: 'Chả cá thác lác um meng', price: 165.000),
      ],
      qrData: 'hihihaha test printer receipt',
      logoPath: 'assets/icon/icon.png',
    );

    // // Hiển thị widget (tuỳ chọn)
    // // ignore: inference_failure_on_function_invocation
    // await showDialog(
    //   context: context,
    //   builder: (_) => Dialog(
    //     child: receiptWidget)
    // );

    // final items = [
    //           ReceiptItem(name: 'Chả cá thác lác um meng nhiều chả ít meng Đậu khuôn chiên buns ddau mam tom cho nhieu tom it bun', price: 165000.00),
    //           ReceiptItem(name: 'Đậu khuôn chiên', price: 40000.00),
    //           ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
    //           ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
    //           ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
    //           ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    //           ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
    //           ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
    //           ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
    //           ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    //           ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
    //           ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
    //           ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
    //           ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    //           ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
    //           ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
    //           ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
    //           ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    //           ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
    //           ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
    //           ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
    //           ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    //         ];

    //         // Tạo instance của CaptureReceiptPrinter
    //         final printer = CaptureReceiptPrinter();

    //         // Chụp ảnh hóa đơn
    //         final Uint8List? imageBytes = await printer.captureReceiptAsImage(
    //           context,
    //           items: items,
    //           qrData: 'hihihaha test printer receipt',
    //           logoPath: 'assets/icon/icon.png',
    //         );

    //         if (imageBytes != null) {
    //           // In hóa đơn
    //           await printer.printReceiptImage(
    //             imageBytes,
    //             // ipAddress: '*************', // IP của máy in
    //           );
    //         } else {
    //           ScaffoldMessenger.of(context).showSnackBar(
    //             const SnackBar(
    //               content: Text('Lỗi: Không thể tạo hình ảnh hóa đơn'),
    //             ),
    //           );
    //         }

    // /// IN trực tiếp với text
    final receiptItems1 = [
      ReceiptItem(name: 'Nước ngọt', price: 123456789101112, quantity: 3),
      ReceiptItem(
          name:
              'Bánh mì nướng muối ớt cho thật nhiều ớt đủ các thể loại, ớt bột ớt xanh ớt đổ từa lưa',
          price: 15000,
          quantity: 1),
      ReceiptItem(
          name: 'Ẩm thực Trung Hoa: 中华美食：zhōnghuá měishí',
          price: 123456,
          quantity: 3),
      // ReceiptItem(name: 'Khăn giấy', price: 3000, quantity: 2),
    ];
    await receiptPrinter.printReceiptAsText(
      ipAddress: '*************',
      receiptItems: receiptItems1,
      cash: 50000.0,
      cashierName: 'tèo téo teo tèn ten',
      logoPath: 'assets/icon/icon.png',
    );
    // Navigator.push(
    //                     context,
    //                     MaterialPageRoute(
    //                       builder: (context) => InvoicePreviewWidget(
    //                         invoiceData: _createTestInvoiceData(),
    //                       ),
    //                     ),
    //                   );

    // ThermalPrinterWidget(
    //                         invoiceData: _createTestInvoiceData(),
    //                       );
  }

  Future<void> onPrintOptimized(BuildContext context) async {
    final items = [
      // ReceiptItem(
      //     name: 'Chả cá thác lác um meng nhiều chả ít meng', price: 165000.00),
      // ReceiptItem(name: 'Đậu khuôn chiên', price: 40000.00),
      // ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      // ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      // ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      // ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
      ReceiptItem(
          name:
              'Chả cá thác lác um meng nhiều chả ít meng Đậu khuôn chiên buns ddau mam tom cho nhieu tom it bun',
          price: 165000.00),
      ReceiptItem(name: 'Đậu khuôn chiên', price: 40000.00),
      ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
      ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
      ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
      ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
      ReceiptItem(name: 'Bún đậu mắm tôm', price: 60000.00),
      ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900000.00),
      ReceiptItem(name: 'Bún chả Hà Nội', price: 200000.00),
      ReceiptItem(name: 'Chả cá thác lác um meng', price: 165000.00),
    ];

    // final printer = OptimizedReceiptPrinter();
    // final imageBytes = await printer.captureReceiptAsImage(
    //   context,
    //   items: items,
    //   qrData: 'hihihaha test printer receipt',
    //   logoPath: 'assets/icon/icon.png',
    // );

    // if (imageBytes != null) {
    //   await printer.printReceiptImage(imageBytes);
    // }
    final printer = OptimizedReceiptPrinter();
// final isConnected = await printer.testPrinterConnection();
// print('Printer connected: $isConnected');

    final imageBytes = await printer.captureReceiptAsImage(
      context,
      items: items,
      qrData: 'test-qr-data',
      logoPath: 'assets/icon/icon.png',
    );

// In bằng method đơn giản
    if (imageBytes != null) {
      await printer.printReceiptImageSimple(imageBytes);
    }
  }

  Future<void> onPrintStar(BuildContext context) async {
    final UniversalReceiptPrinter universalReceiptPrinter =
        UniversalReceiptPrinter();
    print('day ne');
    // await universalReceiptPrinter.testPrinter('*************');

// Test 2: Test với commands
// await universalReceiptPrinter.testPrinterWithCommands('*************');

// Test 3: Test manual IP cho STAR
final starPrinter = UniversalReceiptPrinter();
// await starPrinter.printReceiptStar(
//   ipAddress: '*************', // Thử với IP STAR thật
//   receiptItems: [ReceiptItem(name: 'Test Nếu bạn thấy vẫn có vấn đề với chiều rộng, có thể điều chỉnh paperWidth từ 48 xuống', price: 1000, quantity: 1, description: 'Nếu bạn thấy vẫn có vấn đề với chiều rộng, có thể điều chỉnh paperWidth từ 48 xuống 42 hoặc 44 tùy vào cài đặt font của máy in')],
//   cash: 5000.0,
//   cashierName: 'Test',
//   logoPath: 'https://stickyqr-dev.s3.ap-southeast-1.amazonaws.com/clmiujw5200aiqlta3w1dq200/media/TXYW4kvzZSYfSkU9HnDEI'
// );
await starPrinter.printStarWithEscPos(
  ipAddress: "*************", // IP máy in của bạn
  logoPath: "assets/icon/icon.png", // Path logo trong assets
);

    // await universalReceiptPrinter.testPrinter('*************');

// // Nếu kết nối OK, sử dụng thư viện:
//     await universalReceiptPrinter.printReceiptWithLibrary(
//       ipAddress: '*************',
//       receiptItems: [
//         ReceiptItem(name: 'Nước ngọt', price: 12345678910, quantity: 3),
//         ReceiptItem(
//             name: 'Bánh mì nướng muối ớt...', price: 15000, quantity: 1),
//         // ReceiptItem(name: 'code k dau thi k dau', price: 12345678910, quantity: 3),
//         // ReceiptItem(
//         //     name: 'alo cha ca thac lac um mang', price: 15000, quantity: 1),
//       ],
//       cash: 50000.0,
//       cashierName: 'tèo téo teo tèn ten',
//       forceType: PrinterType.star, // Force STAR mode
//     );
  }

  Widget _buildTabTitle(String title, Color textColor) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16,
        color: textColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  void _buildCompleted() {
    // context.read<CustomizeKitchenTicketCubit>().getReceiptTemplates();
  }
}
