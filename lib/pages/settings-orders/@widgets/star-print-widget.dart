// ignore_for_file: cascade_invocations, inference_failure_on_instance_creation

import 'dart:io';

import 'package:diacritic/diacritic.dart';
import 'package:esc_pos_printer_lts/esc_pos_printer_lts.dart';
import 'package:esc_pos_utils_lts/esc_pos_utils_lts.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart' as intl;
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/receipt-widget.dart';
import 'package:http/http.dart' as http;

enum PrinterType { epson, star, unknown }

class UniversalReceiptPrinter {
  
  // Phát hiện loại máy in dựa trên IP hoặc tên
  PrinterType detectPrinterType(String ipAddress) {
    // Bạn có thể lưu mapping IP -> printer type
    // Hoặc detect qua network discovery
    // Tạm thời dùng cách đơn giản:
    final printerMappings = {
      '*************': PrinterType.epson,  // EPSON TM-T81III
      '*************': PrinterType.star,   // STAR TSP800II
      // Thêm các IP khác...
    };
    
    return printerMappings[ipAddress] ?? PrinterType.unknown;
  }

  Future<void> printReceiptUniversal({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
    String? logoPath,
  }) async {
    final printerType = detectPrinterType(ipAddress);
    print('printerType $printerType');
    
    switch (printerType) {
      case PrinterType.epson:
      print('object 123 123 ');
        await printReceiptEpson(
          ipAddress: ipAddress,
          receiptItems: receiptItems,
          cash: cash,
          cashierName: cashierName,
          logoPath: logoPath,
        );
        break;
      case PrinterType.star:
        await printReceiptStar(
        // await printReceiptGeneric(
          ipAddress: ipAddress,
          receiptItems: receiptItems,
          cash: cash,
          cashierName: cashierName,
          // logoPath: logoPath,
        );
        break;
      default:
        await printReceiptGeneric(
          ipAddress: ipAddress,
          receiptItems: receiptItems,
          cash: cash,
          cashierName: cashierName,
        );
    }
  }

  // Test function - in text đơn giản
  Future<void> testPrinter(String ipAddress) async {
    try {
      print('Attempting to connect to $ipAddress:9100');
      final socket = await Socket.connect(ipAddress, 9100, timeout: const Duration(seconds: 5));
      print('Connected successfully');
      
      // Test với text đơn giản nhất
      String testText = 'TEST PRINT\n';
      testText += 'Hello World!\n';
      testText += '1234567890\n';
      testText += '================\n';
      testText += '\n\n\n\n';
      
      socket.write(testText);
      await socket.flush();
      await Future.delayed(const Duration(milliseconds: 500));
      await socket.close();
      
      print('Test print completed');
    } catch (e) {
      print('Test print error: $e');
      // Try alternative connection method
      await testPrinterAlternative(ipAddress);
    }
  }

  // Alternative connection method
  Future<void> testPrinterAlternative(String ipAddress) async {
    try {
      print('Trying alternative connection method...');
      
      // Try different ports
      final ports = [9100, 515, 631, 35000];
      
      for (final port in ports) {
        try {
          print('Trying port $port');
          final socket = await Socket.connect(ipAddress, port, timeout: const Duration(seconds: 3));
          
          socket.write('TEST PORT $port\n\n\n');
          await socket.flush();
          await Future.delayed(const Duration(milliseconds: 200));
          await socket.close();
          
          print('Port $port works!');
          return;
        } catch (e) {
          print('Port $port failed: $e');
        }
      }
    } catch (e) {
      print('Alternative connection failed: $e');
    }
  }

  // Test với ESC commands
  Future<void> testPrinterWithCommands(String ipAddress) async {
    try {
      final socket = await Socket.connect(ipAddress, 9100);
      
      final List<int> commands = [];
      
      // Initialize
      commands.addAll([0x1B, 0x40]); // ESC @
      
      // Simple text
      commands.addAll('TEST WITH COMMANDS\n'.codeUnits);
      commands.addAll('Line 1\n'.codeUnits);
      commands.addAll('Line 2\n'.codeUnits);
      commands.addAll('================\n'.codeUnits);
      
      // Multiple line feeds
      commands.addAll('\n\n\n\n\n'.codeUnits);
      
      // Multiple cut attempts
      commands.addAll([0x1B, 0x64, 0x02]); // ESC d 2
      commands.addAll([0x1D, 0x56, 0x00]); // GS V 0
      commands.addAll([0x1D, 0x56, 0x01]); // GS V 1
      
      socket.add(commands);
      await socket.flush();
      await Future.delayed(const Duration(seconds: 1));
      await socket.close();
      
      print('Test with commands completed');
    } catch (e) {
      print('Test with commands error: $e');
    }
  }

  // In cho EPSON (code hiện tại của bạn)
  Future<void> printReceiptEpson({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
    String? logoPath,
  }) async {
    // Code hiện tại của bạn cho EPSON
    // ... (giữ nguyên code cũ)
    // await printReceiptAsText(
    //   ipAddress: ipAddress,
    //   receiptItems: receiptItems,
    //   cash: cash,
    //   cashierName: cashierName,
    //   logoPath: logoPath,
    // );
    print('may in epson $ipAddress');
  }

  // Sử dụng thư viện esc_pos_printer_lts cho cả EPSON và STAR
  Future<void> printReceiptWithLibrary({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
    String? logoPath,
    PrinterType? forceType,
  }) async {
    try {
      final profile = await CapabilityProfile.load();
      final printer = NetworkPrinter(PaperSize.mm80, profile);

      final connectResult = await printer.connect(ipAddress, port: 9100);
      if (connectResult.value == 1) {
        print('Connected to printer successfully');
        
        // Detect printer type
        final printerType = forceType ?? detectPrinterType(ipAddress);
        
        // Common initialization
        printer.reset();
        
        if (printerType == PrinterType.star) {
          // STAR specific initialization
          printer.rawBytes([0x1B, 0x1D, 0x61, 0x00]); // Star alignment reset
          printer.rawBytes([0x1B, 0x1D, 0x74, 0x00]); // Star character set
        } else {
          // EPSON initialization (default)
          printer.setGlobalCodeTable('CP1252');
        }

        // Print logo if provided
        if (logoPath != null) {
          try {
            final ByteData data = await rootBundle.load(logoPath);
            final Uint8List bytes = data.buffer.asUint8List();
            const logoWidth = 100;
            const logoHeight = 100;
            final img.Image? originalImage = img.decodeImage(bytes);
            if (originalImage != null) {
              final img.Image logoImage = img.copyResize(
                originalImage,
                width: logoWidth,
                height: logoHeight,
                interpolation: img.Interpolation.linear,
              );
              printer.image(logoImage, align: PosAlign.center);
            }
          } catch (e) {
            print('Logo printing error: $e');
          }
        }

        // Header
        if (printerType == PrinterType.star) {
          printer.rawBytes([0x1B, 0x1D, 0x61, 0x01]); // Star center align
        } else {
          printer.rawBytes([0x1B, 0x61, 0x01]); // EPSON center align
        }

        printer.text(
          removeDiacritics('Quán New 62'),
          styles: const PosStyles(
            align: PosAlign.center,
            bold: true,
            width: PosTextSize.size2,
            height: PosTextSize.size2,
          ),
        );

        printer.feed(1);
        printer.text(
          removeDiacritics('PHIẾU TÍNH TIỀN'),
          styles: const PosStyles(
            align: PosAlign.center,
            bold: true,
            width: PosTextSize.size2,
          ),
        );

        printer.feed(1);
        printer.hr(ch: '=', linesAfter: 1);

        // Reset to left align
        if (printerType == PrinterType.star) {
          printer.rawBytes([0x1B, 0x1D, 0x61, 0x00]);
        } else {
          printer.rawBytes([0x1B, 0x61, 0x00]);
        }

        // Date and cashier info
        printer.row([
          PosColumn(
            text: removeDiacritics('Ngày bán: '),
            width: 5,
            styles: const PosStyles(align: PosAlign.left),
          ),
          PosColumn(
            text: removeDiacritics('${DateTime.now()}'),
            width: 7,
            styles: const PosStyles(align: PosAlign.right),
          ),
        ]);

        printer.row([
          PosColumn(
            text: removeDiacritics('Nhân viên: '),
            width: 5,
            styles: const PosStyles(align: PosAlign.left),
          ),
          PosColumn(
            text: removeDiacritics(cashierName),
            width: 7,
            styles: const PosStyles(align: PosAlign.right),
          ),
        ]);

        printer.feed(1);
        printer.hr(ch: '=', linesAfter: 0);

        // Column headers
        printer.row([
          PosColumn(
            text: removeDiacritics('Mặt hàng'),
            width: 8,
            styles: const PosStyles(align: PosAlign.left, bold: true),
          ),
          PosColumn(
            text: 'SL',
            width: 1,
            styles: const PosStyles(align: PosAlign.center, bold: true),
          ),
          PosColumn(
            text: removeDiacritics('Giá'),
            width: 3,
            styles: const PosStyles(align: PosAlign.right, bold: true),
          ),
        ]);

        printer.hr(ch: '-');

        // Print items
        for (final item in receiptItems) {
          final nameLines = _wrapText(removeDiacritics(item.name), 20);
          final priceFormatted = '\$${intl.NumberFormat('#,##0', 'en_US').format(item.price)}';
          
          printer.row([
            PosColumn(
              text: nameLines.first,
              width: 8,
              styles: const PosStyles(align: PosAlign.left),
            ),
            PosColumn(
              text: '${item.quantity ?? 1}',
              width: 1,
              styles: const PosStyles(align: PosAlign.center),
            ),
            PosColumn(
              text: priceFormatted,
              width: 3,
              styles: const PosStyles(align: PosAlign.right),
            ),
          ]);

          // Additional lines for long names
          for (int i = 1; i < nameLines.length; i++) {
            printer.row([
              PosColumn(
                text: nameLines[i],
                width: 8,
                styles: const PosStyles(align: PosAlign.left),
              ),
              PosColumn(text: '', width: 1),
              PosColumn(text: '', width: 3),
            ]);
          }
        }

        // Calculate total
        final total = receiptItems.fold(0.0, (sum, item) => sum + (item.price * (item.quantity ?? 1)));

        printer.hr(ch: '=');
        printer.row([
          PosColumn(text: 'TONG CONG:', width: 5, styles: const PosStyles(bold: true, align: PosAlign.left)),
          PosColumn(text: '', width: 2),
          PosColumn(text: '\$${intl.NumberFormat('#,##0').format(total)}', width: 5, styles: const PosStyles(bold: true, align: PosAlign.right)),
        ]);

        printer.row([
          PosColumn(text: 'TIEN MAT:', width: 5, styles: const PosStyles(align: PosAlign.left)),
          PosColumn(text: '', width: 2),
          PosColumn(text: '\$${intl.NumberFormat('#,##0').format(cash)}', width: 5, styles: const PosStyles(align: PosAlign.right)),
        ]);

        printer.row([
          PosColumn(text: 'TIEN THOI:', width: 5, styles: const PosStyles(align: PosAlign.left)),
          PosColumn(text: '', width: 2),
          PosColumn(text: '\$${intl.NumberFormat('#,##0').format(cash - total)}', width: 5, styles: const PosStyles(align: PosAlign.right)),
        ]);

        printer.hr(ch: '=');

        // Footer
        if (printerType == PrinterType.star) {
          printer.rawBytes([0x1B, 0x1D, 0x61, 0x01]); // Star center align
        } else {
          printer.rawBytes([0x1B, 0x61, 0x01]); // EPSON center align
        }

        printer.text(
          removeDiacritics('Cam on quy khach!'),
          // 'Cảm ơn quý khách',
          styles: const PosStyles(align: PosAlign.center, bold: true),
        );

        printer.feed(3);
        printer.cut();

        printer.disconnect();
        print('Receipt printed successfully');
      } else {
        print('Cannot connect to printer');
      }
    } catch (e) {
      print('Print error: $e');
    }
  }

  // // Helper function to wrap text
  // List<String> _wrapText(String text, int maxWidth) {
  //   final List<String> lines = [];
  //   String currentLine = '';

  //   for (final String word in text.split(' ')) {
  //     if ((currentLine + (currentLine.isEmpty ? '' : ' ') + word).length <= maxWidth) {
  //       currentLine += (currentLine.isEmpty ? '' : ' ') + word;
  //     } else {
  //       if (currentLine.isNotEmpty) lines.add(currentLine);
  //       currentLine = word;
  //     }
  //   }
  //   if (currentLine.isNotEmpty) lines.add(currentLine);
  //   return lines;
  // }
  // Future<void> printReceiptEpson({
  //   required String ipAddress,
  //   required List<ReceiptItem> receiptItems,
  //   required double cash,
  //   required String cashierName,
  //   String? logoPath,
  // }) async {
  //   // Code hiện tại của bạn cho EPSON
  //   // ... (giữ nguyên code cũ)
  //   await printReceiptAsText(
  //     ipAddress: ipAddress,
  //     receiptItems: receiptItems,
  //     cash: cash,
  //     cashierName: cashierName,
  //     logoPath: logoPath,
  //   );
  // }

  // Code cũ của bạn cho EPSON (giữ nguyên)
  Future<void> printReceiptAsText({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
    String? logoPath,
  }) async {
    // ... (code cũ của bạn)
    // Tôi sẽ giữ nguyên code này
  }

  // In cho STAR sử dụng Raw Socket
  // Future<void> printReceiptStar({
  //   required String ipAddress,
  //   required List<ReceiptItem> receiptItems,
  //   required double cash,
  //   required String cashierName,
  //   String? logoPath,
  // }) async {
  //   try {
  //     final socket = await Socket.connect(ipAddress, 9100);
      
  //     // Star Line Mode Commands
  //     final List<int> commands = [];
      
  //     // Initialize printer
  //     commands.addAll([0x1B, 0x40]); // ESC @
      
  //     // Set character set
  //     commands.addAll([0x1B, 0x1D, 0x74, 0x00]); // ESC GS t 0
      
  //     // Header - Center align
  //     commands.addAll([0x1B, 0x1D, 0x61, 0x01]); // ESC GS a 1 (center)
      
  //     // Bold on
  //     commands.addAll([0x1B, 0x45]); // ESC E
  //     commands.addAll('Quan New 62\n'.codeUnits);
  //     commands.addAll([0x1B, 0x46]); // ESC F (bold off)
      
  //     commands.addAll([0x1B, 0x45]); // ESC E
  //     commands.addAll('PHIEU TINH TIEN\n'.codeUnits);
  //     commands.addAll([0x1B, 0x46]); // ESC F
      
  //     // Left align
  //     commands.addAll([0x1B, 0x1D, 0x61, 0x00]); // ESC GS a 0 (left)
      
  //     // Separator
  //     commands.addAll('================================\n'.codeUnits);
      
  //     // Date and cashier
  //     final dateStr = DateTime.now().toString().substring(0, 19);
  //     commands.addAll('Ngay ban: $dateStr\n'.codeUnits);
  //     commands.addAll('Nhan vien: ${removeDiacritics(cashierName)}\n'.codeUnits);
  //     commands.addAll('================================\n'.codeUnits);
      
  //     // Headers
  //     commands.addAll('Mat hang         SL    Gia\n'.codeUnits);
  //     commands.addAll('--------------------------------\n'.codeUnits);
      
  //     // Items
  //     for (final item in receiptItems) {
  //       final name = removeDiacritics(item.name);
  //       final price = intl.NumberFormat('#,##0').format(item.price);
  //       final quantity = item.quantity?.toString() ?? '1';
        
  //       // Format line (adjust spacing as needed)
  //       String line = name.padRight(16);
  //       line += quantity.padLeft(3);
  //       line += '\$$price\n';
        
  //       commands.addAll(line.codeUnits);
  //     }
      
  //     // Total
  //     final total = receiptItems.fold(0.0, (sum, item) => sum + (item.price * (item.quantity ?? 1)));
  //     commands.addAll('================================\n'.codeUnits);
  //     commands.addAll('TONG CONG:              \$${intl.NumberFormat('#,##0').format(total)}\n'.codeUnits);
  //     commands.addAll('TIEN MAT:               \$${intl.NumberFormat('#,##0').format(cash)}\n'.codeUnits);
  //     commands.addAll('TIEN THOI:              \$${intl.NumberFormat('#,##0').format(cash - total)}\n'.codeUnits);
  //     commands.addAll('================================\n'.codeUnits);
      
  //     // Footer - Center align
  //     commands.addAll([0x1B, 0x1D, 0x61, 0x01]); // ESC GS a 1
  //     commands.addAll('Cam on quy khach!\n'.codeUnits);
      
  //     // Add more line feeds
  //     commands.addAll('\n\n\n\n'.codeUnits);
      
  //     // Cut paper - try different cut commands
  //     commands.addAll([0x1B, 0x64, 0x02]); // ESC d 2 (full cut)
  //     // Alternative: commands.addAll([0x1D, 0x56, 0x00]); // GS V 0
      
  //     // Send to printer
  //     socket.add(commands);
  //     await socket.flush();
  //     await Future.delayed(const Duration(milliseconds: 500)); // Wait for processing
  //     await socket.close();
      
  //     print('In hoa don STAR thanh cong');
  //   } catch (e) {
  //     print('Loi in hoa don STAR: $e');
  //   }
  // }
  Future<void> printReceiptStar({
  required String ipAddress,
  required List<ReceiptItem> receiptItems,
  required double cash,
  required String cashierName,
  String? logoPath,
}) async {
  try {
    final socket = await Socket.connect(ipAddress, 9100);
    
    // Star Line Mode Commands
    final List<int> commands = [];
    
    // Paper width for 80mm = 42 characters (safe width)
    const int paperWidth = 42;
    // Price column width 
    const int priceColumnWidth = 8;
    const int itemColumnWidth = 32; // Leave some buffer
    
    // Initialize printer
    commands.addAll([0x1B, 0x40]); // ESC @ - Initialize
    
    // ================== HEADER SECTION ==================
    // Simple text logo (no complex bitmap)
    if (logoPath != null) {
      final logoText = '[ RESTAURANT LOGO ]';
      commands.addAll('${_centerText(logoText, paperWidth)}\n'.codeUnits);
      commands.addAll('\n'.codeUnits);
    }
    
    // Restaurant name - Bold and manually centered
    commands.addAll([0x1B, 0x45]); // ESC E (bold on)
    final restaurantName = 'Pho Bac Hoa Viet - South Sac';
    final restaurantLines = _wrapText(restaurantName, paperWidth);
    for (final line in restaurantLines) {
      commands.addAll('${_centerText(line, paperWidth)}\n'.codeUnits);
    }
    commands.addAll([0x1B, 0x46]); // ESC F (bold off)
    
    // Address - wrap and center if needed
    final address = '6645 Stockton Blvd, Sacramento';
    final addressLines = _wrapText(address, paperWidth);
    for (final line in addressLines) {
      commands.addAll('${_centerText(line, paperWidth)}\n'.codeUnits);
    }
    
    // Phone - centered
    commands.addAll('${_centerText('+1**************', paperWidth)}\n'.codeUnits);
    commands.addAll('\n'.codeUnits);
    
    // ================== CUSTOMER INFO SECTION ==================
    // Dotted line separator
    commands.addAll('${'-' * paperWidth}\n'.codeUnits);
    
    // Customer info
    commands.addAll('Customer name\n'.codeUnits);
    commands.addAll([0x1B, 0x45]); // Bold on
    final customerName = 'Julie Pham';
    commands.addAll('$customerName\n'.codeUnits);
    commands.addAll([0x1B, 0x46]); // Bold off
    commands.addAll('\n'.codeUnits);
    
    // Order time
    commands.addAll('Order time\n'.codeUnits);
    final now = DateTime.now();
    final orderTime = '${_getWeekday(now.weekday)}, ${_getMonth(now.month)} ${now.day}, ${_formatTime(now)}';
    commands.addAll([0x1B, 0x45]); // Bold on
    commands.addAll('$orderTime\n'.codeUnits);
    commands.addAll([0x1B, 0x46]); // Bold off
    commands.addAll('\n'.codeUnits);
    
    // Order number
    commands.addAll('Order number\n'.codeUnits);
    commands.addAll([0x1B, 0x45]); // Bold on
    commands.addAll('#4322\n'.codeUnits);
    commands.addAll([0x1B, 0x46]); // Bold off
    commands.addAll('\n'.codeUnits);
    
    // Total items
    commands.addAll('Total\n'.codeUnits);
    commands.addAll([0x1B, 0x45]); // Bold on
    commands.addAll('${receiptItems.length} Items\n'.codeUnits);
    commands.addAll([0x1B, 0x46]); // Bold off
    
    // Dotted line separator
    commands.addAll('${'-' * paperWidth}\n'.codeUnits);
    commands.addAll('\n'.codeUnits);
    
    // ================== ITEMS SECTION ==================
    for (int i = 0; i < receiptItems.length; i++) {
      final item = receiptItems[i];
      final quantity = item.quantity ?? 1;
      final totalPrice = item.price * quantity;
      final priceStr = '\$${totalPrice.toStringAsFixed(2)}';
      
      // Item text
      final itemText = '$quantity x ${i + 51}. ${item.name}';
      
      // Simple approach: item name on one line, price on right
      if (itemText.length + priceStr.length + 1 <= paperWidth) {
        // Single line: fits perfectly
        final spacesNeeded = paperWidth - itemText.length - priceStr.length;
        final line = itemText + (' ' * spacesNeeded) + priceStr;
        commands.addAll('$line\n'.codeUnits);
      } else {
        // Multi-line: wrap item text, then price on separate line or right-aligned
        final wrappedLines = _wrapText(itemText, itemColumnWidth);
        
        // Print wrapped item lines
        for (int j = 0; j < wrappedLines.length; j++) {
          if (j == 0) {
            // First line with price on right if space allows
            final firstLine = wrappedLines[j];
            if (firstLine.length + priceStr.length + 1 <= paperWidth) {
              final spacesNeeded = paperWidth - firstLine.length - priceStr.length;
              final line = firstLine + (' ' * spacesNeeded) + priceStr;
              commands.addAll('$line\n'.codeUnits);
            } else {
              // Not enough space, print item and price separately
              commands.addAll('${wrappedLines[j]}\n'.codeUnits);
              commands.addAll('${_rightAlign(priceStr, paperWidth)}\n'.codeUnits);
            }
          } else {
            commands.addAll('${wrappedLines[j]}\n'.codeUnits);
          }
        }
      }
      
      // Item description/notes (if any)
      if (item.description != null && item.description!.isNotEmpty) {
        final descLines = _wrapText('  ${item.description}', paperWidth);
        for (final line in descLines) {
          commands.addAll('$line\n'.codeUnits);
        }
      }
      
      commands.addAll('\n'.codeUnits);
    }
    
    // Dotted line separator
    commands.addAll('${'-' * paperWidth}\n'.codeUnits);
    
    // ================== TOTALS SECTION ==================
    final subtotal = receiptItems.fold(0.0, (sum, item) => sum + (item.price * (item.quantity ?? 1)));
    final tax = subtotal * 0.0875;
    final total = subtotal + tax;
    
    // Subtotal
    commands.addAll(_formatTotalLine('Subtotal', subtotal, paperWidth));
    
    // Tax
    commands.addAll(_formatTotalLine('Tax', tax, paperWidth));
    
    // Total - Bold
    commands.addAll([0x1B, 0x45]); // Bold on
    commands.addAll(_formatTotalLine('Total', total, paperWidth));
    commands.addAll([0x1B, 0x46]); // Bold off
    
    // ================== FOOTER SECTION ==================
    commands.addAll('\n'.codeUnits);
    
    // Thank you - manually centered
    commands.addAll([0x1B, 0x45]); // Bold on
    commands.addAll('${_centerText('THANK YOU!', paperWidth)}\n'.codeUnits);
    commands.addAll([0x1B, 0x46]); // Bold off
    
    // Add extra lines for cutting
    commands.addAll('\n\n\n\n'.codeUnits);
    
    // Cut paper
    commands.addAll([0x1B, 0x64, 0x02]); // ESC d 2 (full cut)
    
    // Send to printer
    socket.add(commands);
    await socket.flush();
    await Future.delayed(const Duration(milliseconds: 1000)); // Increased delay
    await socket.close();
    
    print('In hoa don STAR thanh cong');
  } catch (e) {
    print('Loi in hoa don STAR: $e');
  }
}

// Helper functions
String _getWeekday(int weekday) {
  const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
  return weekdays[weekday - 1];
}

String _getMonth(int month) {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  return months[month - 1];
}

String _formatTime(DateTime dateTime) {
  final hour = dateTime.hour;
  final minute = dateTime.minute.toString().padLeft(2, '0');
  final period = hour >= 12 ? 'PM' : 'AM';
  final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
  return '$displayHour:$minute $period';
}

// Text wrapping function
List<String> _wrapText(String text, int maxWidth) {
  if (text.length <= maxWidth) {
    return [text];
  }
  
  final List<String> lines = [];
  final words = text.split(' ');
  String currentLine = '';
  
  for (final word in words) {
    if (word.length > maxWidth) {
      // If a single word is longer than maxWidth, break it
      if (currentLine.isNotEmpty) {
        lines.add(currentLine.trim());
        currentLine = '';
      }
      
      for (int i = 0; i < word.length; i += maxWidth) {
        final end = (i + maxWidth < word.length) ? i + maxWidth : word.length;
        lines.add(word.substring(i, end));
      }
    } else if ((currentLine + word).length <= maxWidth) {
      currentLine += (currentLine.isEmpty ? '' : ' ') + word;
    } else {
      if (currentLine.isNotEmpty) {
        lines.add(currentLine.trim());
      }
      currentLine = word;
    }
  }
  
  if (currentLine.isNotEmpty) {
    lines.add(currentLine.trim());
  }
  
  return lines.isEmpty ? [''] : lines;
}

// Center text manually
String _centerText(String text, int width) {
  if (text.length >= width) return text;
  final padding = (width - text.length) ~/ 2;
  return ' ' * padding + text;
}

// Right align text
String _rightAlign(String text, int width) {
  if (text.length >= width) return text;
  final spaces = width - text.length;
  return ' ' * spaces + text;
}

// Format total line with proper spacing
List<int> _formatTotalLine(String label, double amount, int width) {
  final amountStr = '\$${amount.toStringAsFixed(2)}';
  final totalLength = label.length + amountStr.length;
  
  if (totalLength >= width) {
    // If too long, put on separate lines
    final lines = _wrapText(label, width);
    final result = <int>[];
    for (final line in lines) {
      result.addAll('$line\n'.codeUnits);
    }
    result.addAll('${_rightAlign(amountStr, width)}\n'.codeUnits);
    return result;
  } else {
    // Normal case with spacing
    final spacesNeeded = width - totalLength;
    return '$label${' ' * spacesNeeded}$amountStr\n'.codeUnits;
  }
}

Future<void> printStarImageFromUrl({
  required String ipAddress,
  required String imageUrl,
}) async {
  try {
    final socket = await Socket.connect(ipAddress, 9100);
    final List<int> commands = [];
    
    print('Downloading image from: $imageUrl');
    
    // Download image from URL
    final response = await http.get(Uri.parse(imageUrl));
    if (response.statusCode != 200) {
      throw Exception('Failed to download image: ${response.statusCode}');
    }
    
    final Uint8List imageBytes = response.bodyBytes;
    print('Image downloaded, size: ${imageBytes.length} bytes');
    
    // Initialize printer
    commands.addAll([0x1B, 0x40]); // ESC @ - Initialize
    
    // Process image
    final img.Image? originalImage = img.decodeImage(imageBytes);
    if (originalImage == null) {
      throw Exception('Cannot decode image');
    }
    
    print('Original image: ${originalImage.width}x${originalImage.height}');
    
    // Resize to very small size for testing
    const int maxWidth = 100;
    final double aspectRatio = originalImage.height / originalImage.width;
    final int targetHeight = (maxWidth * aspectRatio).round();
    
    img.Image resizedImage = img.copyResize(
      originalImage,
      width: maxWidth,
      height: targetHeight,
      interpolation: img.Interpolation.nearest, // Use nearest neighbor to avoid errors
    );
    
    print('Resized image: ${resizedImage.width}x${resizedImage.height}');
    
    // Convert to bitmap using simple method
    final imageCommands = _convertToSimpleBitmap(resizedImage);
    commands.addAll(imageCommands);
    
    // Add some text below image
    commands.addAll([0x0A, 0x0A]); // Line feeds
    commands.addAll([0x1B, 0x61, 0x01]); // Center align
    commands.addAll('IMAGE TEST SUCCESSFUL'.codeUnits);
    commands.addAll([0x0A]);
    commands.addAll([0x1B, 0x61, 0x00]); // Left align
    
    // Cut paper
    commands.addAll([0x0A, 0x0A, 0x0A]);
    commands.addAll([0x1D, 0x56, 0x42, 0x00]); // Full cut
    
    // Send to printer
    socket.add(commands);
    await socket.flush();
    await Future.delayed(const Duration(milliseconds: 3000));
    await socket.close();
    
    print('STAR image printing completed successfully');
    
  } catch (e) {
    print('Error printing STAR image: $e');
  }
}

// Simple bitmap conversion - avoid complex pixel operations
List<int> _convertToSimpleBitmap(img.Image image) {
  final List<int> commands = [];
  
  try {
    // Center alignment
    commands.addAll([0x1B, 0x61, 0x01]); // ESC a 1
    
    final int width = image.width;
    final int height = image.height;
    final int bytesPerLine = (width + 7) ~/ 8;
    
    print('Processing bitmap: ${width}x${height}, bytes per line: $bytesPerLine');
    
    // Simple monochrome conversion
    final List<List<bool>> pixelMatrix = [];
    
    for (int y = 0; y < height; y++) {
      final List<bool> row = [];
      for (int x = 0; x < width; x++) {
        try {
          final pixel = image.getPixel(x, y);
          
          // Safe color extraction
          int r, g, b;
          if (pixel is img.ColorRgb8) {
            r = pixel.r;
            g = pixel.g;
            b = pixel.b;
          } else {
            // Fallback for other color formats
            final rgba = pixel.toList();
            r = rgba.isNotEmpty ? rgba[0].toInt() : 0;
            g = rgba.length > 1 ? rgba[1].toInt() : 0;
            b = rgba.length > 2 ? rgba[2].toInt() : 0;
          }
          
          // Convert to grayscale using simple average
          final gray = (r + g + b) ~/ 3;
          
          // Threshold: < 128 = black (true), >= 128 = white (false)
          row.add(gray < 128);
        } catch (e) {
          print('Error processing pixel at ($x, $y): $e');
          row.add(false); // Default to white
        }
      }
      pixelMatrix.add(row);
    }
    
    print('Pixel matrix created successfully');
    
    // Convert to bitmap commands
    for (int y = 0; y < height; y++) {
      // ESC * m command for single line
      commands.addAll([0x1B, 0x2A, 0x00]); // ESC * 0 (8-pin single density)
      commands.add(bytesPerLine & 0xFF);    // Width low byte
      commands.add((bytesPerLine >> 8) & 0xFF); // Width high byte
      
      // Convert row to bytes
      for (int x = 0; x < bytesPerLine; x++) {
        int byteValue = 0;
        
        for (int bit = 0; bit < 8; bit++) {
          final pixelX = x * 8 + bit;
          if (pixelX < width && pixelMatrix[y][pixelX]) {
            byteValue |= (0x80 >> bit); // Set bit if pixel is black
          }
        }
        
        commands.add(byteValue);
      }
      
      commands.add(0x0A); // Line feed
    }
    
    // Back to left alignment
    commands.addAll([0x1B, 0x61, 0x00]); // ESC a 0
    
    print('Bitmap conversion completed');
    
  } catch (e) {
    print('Error in bitmap conversion: $e');
    // Return empty commands if conversion fails
    return [];
  }
  
  return commands;
}

// Even simpler version - just test with a basic pattern
Future<void> printTestPattern({required String ipAddress}) async {
  try {
    final socket = await Socket.connect(ipAddress, 9100);
    final List<int> commands = [];
    
    // Initialize
    commands.addAll([0x1B, 0x40]); // ESC @
    
    // Center align
    commands.addAll([0x1B, 0x61, 0x01]);
    
    // Create a simple test pattern (10x10 checkerboard)
    const int size = 20;
    const int bytesPerLine = (size + 7) ~/ 8;
    
    for (int y = 0; y < size; y++) {
      // Bitmap command
      commands.addAll([0x1B, 0x2A, 0x00]); // ESC * 0
      commands.add(bytesPerLine & 0xFF);
      commands.add((bytesPerLine >> 8) & 0xFF);
      
      // Create checkerboard pattern
      for (int x = 0; x < bytesPerLine; x++) {
        int byteValue = 0;
        for (int bit = 0; bit < 8; bit++) {
          final pixelX = x * 8 + bit;
          if (pixelX < size) {
            // Checkerboard: alternate every 2 pixels
            final isBlack = ((pixelX ~/ 2) + (y ~/ 2)) % 2 == 0;
            if (isBlack) {
              byteValue |= (0x80 >> bit);
            }
          }
        }
        commands.add(byteValue);
      }
      
      commands.add(0x0A); // Line feed
    }
    
    // Add text
    commands.addAll([0x0A]);
    commands.addAll('TEST PATTERN'.codeUnits);
    commands.addAll([0x0A, 0x0A, 0x0A]);
    
    // Cut
    commands.addAll([0x1D, 0x56, 0x42, 0x00]);
    
    socket.add(commands);
    await socket.flush();
    await Future.delayed(const Duration(milliseconds: 2000));
    await socket.close();
    
    print('Test pattern printed');
  } catch (e) {
    print('Test pattern error: $e');
  }
}

// Helper function for assets loading (if needed later)
Future<void> printStarImageFromAssets({
  required String ipAddress,
  required String assetPath,
}) async {
  try {
    final socket = await Socket.connect(ipAddress, 9100);
    final List<int> commands = [];
    
    // Load from assets
    final ByteData data = await rootBundle.load(assetPath);
    final Uint8List bytes = data.buffer.asUint8List();
    
    // Initialize printer
    commands.addAll([0x1B, 0x40]);
    
    // Decode and process image
    final img.Image? image = img.decodeImage(bytes);
    if (image != null) {
      // Small resize
      final resized = img.copyResize(image, width: 80, height: 80);
      final imageCommands = _convertToSimpleBitmap(resized);
      commands.addAll(imageCommands);
    }
    
    // Text
    commands.addAll([0x0A, 0x0A]);
    commands.addAll([0x1B, 0x61, 0x01]);
    commands.addAll('ASSET IMAGE TEST'.codeUnits);
    commands.addAll([0x0A, 0x0A, 0x0A]);
    
    // Cut
    commands.addAll([0x1D, 0x56, 0x42, 0x00]);
    
    socket.add(commands);
    await socket.flush();
    await Future.delayed(const Duration(milliseconds: 2000));
    await socket.close();
    
    print('Asset image printed');
  } catch (e) {
    print('Asset image error: $e');
  }
}

// // Helper functions
// String _getWeekday(int weekday) {
//   const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
//   return weekdays[weekday - 1];
// }

// String _getMonth(int month) {
//   const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
//                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
//   return months[month - 1];
// }

// String _formatTime(DateTime dateTime) {
//   final hour = dateTime.hour;
//   final minute = dateTime.minute.toString().padLeft(2, '0');
//   final period = hour >= 12 ? 'PM' : 'AM';
//   final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
//   return '$displayHour:$minute $period';
// }

String _truncateText(String text, int maxLength) {
  if (text.length <= maxLength) return text;
  return '${text.substring(0, maxLength - 3)}...';
}

// List<int> _formatTotalLine(String label, double amount) {
//   final amountStr = '\$${amount.toStringAsFixed(2)}';
//   final spacesNeeded = 42 - label.length - amountStr.length;
//   return '$label${' ' * spacesNeeded}$amountStr\n'.codeUnits;
// }

  // In generic - chỉ dùng text thuần (compatible với hầu hết máy in)
  Future<void> printReceiptGeneric({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
  }) async {
    try {
      final socket = await Socket.connect(ipAddress, 9100);
      
      String receipt = '';
      
      // Header
      receipt += '        Quan New 62\n';
      receipt += '    PHIEU TINH TIEN\n';
      receipt += '================================\n';
      
      // Date and cashier
      final dateStr = DateTime.now().toString().substring(0, 19);
      receipt += 'Ngay ban: $dateStr\n';
      receipt += 'Nhan vien: ${removeDiacritics(cashierName)}\n';
      receipt += '================================\n';
      
      // Headers
      receipt += 'Mat hang         SL    Gia\n';
      receipt += '--------------------------------\n';
      
      // Items
      for (final item in receiptItems) {
        final name = removeDiacritics(item.name);
        final price = intl.NumberFormat('#,##0').format(item.price);
        final quantity = item.quantity?.toString() ?? '1';
        
        // Simple formatting
        String line = name.length > 16 ? name.substring(0, 16) : name.padRight(16);
        line += quantity.padLeft(3);
        line += '\$$price\n';
        
        receipt += line;
      }
      
      // Total
      final total = receiptItems.fold(0.0, (sum, item) => sum + (item.price * (item.quantity ?? 1)));
      receipt += '================================\n';
      receipt += 'TONG CONG:              \$${intl.NumberFormat('#,##0').format(total)}\n';
      receipt += 'TIEN MAT:               \$${intl.NumberFormat('#,##0').format(cash)}\n';
      receipt += 'TIEN THOI:              \$${intl.NumberFormat('#,##0').format(cash - total)}\n';
      receipt += '================================\n';
      receipt += '      Cam on quy khach!\n';
      receipt += '\n\n\n\n\n'; // More feed lines
      
      // Add cut command for generic printing
      receipt += '\x1B\x64\x02'; // ESC d 2 (cut)
      
      // Send raw text
      socket.write(receipt);
      await socket.flush();
      await Future.delayed(const Duration(milliseconds: 500));
      await socket.close();
      
      print('In hoa don generic thanh cong');
    } catch (e) {
      print('Loi in hoa don generic: $e');
    }
  }
}
