import 'dart:math';

import 'package:diacritic/diacritic.dart';
import 'package:esc_pos_printer_lts/esc_pos_printer_lts.dart';
import 'package:esc_pos_utils_lts/esc_pos_utils_lts.dart';
import 'package:flutter/material.dart';
// import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart' as intl;
import 'package:qr_flutter/qr_flutter.dart';

class ReceiptPrinter {
  Future<void> printReceiptAsText({
    required String ipAddress,
    required List<ReceiptItem> receiptItems,
    required double cash,
    required String cashierName,
    String? logoPath,
  }) async {
    try {
      final profile = await CapabilityProfile.load();
      final printer = NetworkPrinter(PaperSize.mm80, profile);

      final connectResult = await printer.connect(ipAddress, port: 9100);
      if (connectResult.value == 1) {
        // // In logo
        if (logoPath != null) {
          final ByteData data = await rootBundle.load(logoPath);
          final Uint8List bytes = data.buffer.asUint8List();
          if (bytes != null) {
            const logoWidth = 100;
            const logoHeight = 100;
            final img.Image? originalImage = img.decodeImage(bytes);
            if (originalImage != null) {
              // Nếu có chỉ định kích thước, resize hình ảnh
              img.Image logoImage = originalImage;
              if (logoWidth != null || logoHeight != null) {
                logoImage = img.copyResize(
                  originalImage,
                  width: logoHeight != null ? (logoHeight * originalImage.width / originalImage.height).round() : originalImage.width,
                  height: logoWidth != null ? (logoWidth * originalImage.height / originalImage.width).round() : originalImage.height,
                  interpolation: img.Interpolation.linear,
                );
              }

              // In logo với alignment đã chọn
              printer.image(
                logoImage,
                align: PosAlign.center,
              );
              // ..feed(1);
            }
          }
        }

        // Loại bỏ dấu cho tên nhân viên
        final plainCashierName = removeDiacritics(cashierName);

        // Hàm wrap text cho tên sản phẩm
        List<String> wrapItemName(String text, int maxWidth) {
          final List<String> lines = [];
          String currentLine = '';

          for (final String word in text.split(' ')) {
            if ((currentLine + (currentLine.isEmpty ? '' : ' ') + word).length <= maxWidth) {
              currentLine += (currentLine.isEmpty ? '' : ' ') + word;
            } else {
              if (currentLine.isNotEmpty) lines.add(currentLine);
              currentLine = word;
            }
          }
          if (currentLine.isNotEmpty) lines.add(currentLine);
          return lines;
        }

        // Hàm format và wrap giá tiền
        List<String> formatPrice(double price, int maxWidth) {
          final String fullPrice = intl.NumberFormat('#,##0', 'en_US').format(price);
          final List<String> lines = [];

          // Thêm "$" vào đầu số đầu tiên
          String remainingPrice = fullPrice;
          while (remainingPrice.isNotEmpty) {
            if (lines.isEmpty) {
              // Dòng đầu tiên với "$"
              final String line = '\$${remainingPrice.substring(0, min(maxWidth - 1, remainingPrice.length))}';
              lines.add(line);
              if (remainingPrice.length > maxWidth - 1) {
                remainingPrice = remainingPrice.substring(maxWidth - 1);
              } else {
                break;
              }
            } else {
              // Các dòng tiếp theo
              final String line = remainingPrice.substring(0, min(maxWidth, remainingPrice.length));
              lines.add(line);
              if (remainingPrice.length > maxWidth) {
                remainingPrice = remainingPrice.substring(maxWidth);
              } else {
                break;
              }
            }
          }
          return lines;
        }

        // Hàm định dạng số tiền và chia dòng
        List<String> formatAndSplitPrice(double price, int charsPerLine) {
          // Định dạng số với dấu phẩy phân cách hàng nghìn
          String formattedPrice = intl.NumberFormat('#,##0', 'en_US').format(price);
          final List<String> lines = [];

          // Nếu độ dài không vượt quá giới hạn, trả về một dòng
          if (formattedPrice.length <= charsPerLine) {
            return [formattedPrice];
          }

          // Chia thành các dòng với độ dài phù hợp
          while (formattedPrice.length > charsPerLine) {
            int splitIndex = formattedPrice.length - charsPerLine;
            // Điều chỉnh vị trí cắt để không cắt giữa nhóm số
            while (splitIndex > 0 && formattedPrice[splitIndex] == ' ') {
              splitIndex--;
            }

            final String line = formattedPrice.substring(splitIndex);
            formattedPrice = formattedPrice.substring(0, splitIndex);

            // Thêm vào đầu mảng để duy trì thứ tự đúng
            lines.insert(0, line);
          }

          if (formattedPrice.isNotEmpty) {
            lines.insert(0, formattedPrice);
          }

          return lines;
        }

        /// HEADER
        printer
          ..reset() // Reset toàn bộ trạng thái
          ..setGlobalCodeTable('CP1252')
          ..rawBytes([0x1B, 0x61, 0x01]) // Command để căn giữa
          ..text(removeDiacritics('Quán New 62'),
              styles: const PosStyles(
                align: PosAlign.center,
                bold: true,
                width: PosTextSize.size2,
                height: PosTextSize.size2,
              ))
          ..feed(1)
          ..text(removeDiacritics('PHIẾU TÍNH TIỀN'), styles: const PosStyles(align: PosAlign.center, bold: true, width: PosTextSize.size2))
          ..feed(1)
          ..hr(ch: '=', linesAfter: 1)
          // ..text(removeDiacritics('Ngày bán: ${DateTime.now()}'),
          //     styles: const PosStyles(align: PosAlign.left))
          // ..text(removeDiacritics('Nhân viên: $plainCashierName'),
          //     styles: const PosStyles(align: PosAlign.left))
          ..rawBytes([0x1B, 0x61, 0x00]) // Reset về căn trái cho phần tiếp theo
          ..row([
            PosColumn(
              text: (removeDiacritics('Ngày bán: ')),
              width: 5,
              styles: const PosStyles(
                align: PosAlign.left,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: (removeDiacritics('${DateTime.now()}')),
              width: 7,
              styles: const PosStyles(
                align: PosAlign.right,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
          ])
          ..row([
            PosColumn(
              text: (removeDiacritics('Nhân viên: ')),
              width: 5,
              styles: const PosStyles(
                align: PosAlign.left,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: (removeDiacritics(plainCashierName)),
              width: 7,
              styles: const PosStyles(
                align: PosAlign.right,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
          ])
          ..feed(1)
          ..hr(ch: '=', linesAfter: 0)

          // Column headers với full width
          ..row([
            PosColumn(
              text: removeDiacritics('Mặt hàng'),
              width: 8,
              styles: const PosStyles(
                align: PosAlign.left,
                bold: true,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: 'SL',
              width: 1,
              styles: const PosStyles(
                align: PosAlign.center,
                bold: true,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: removeDiacritics('Giá'),
              width: 3,
              styles: const PosStyles(
                align: PosAlign.right,
                bold: true,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
          ])
          ..hr(ch: '-');

        /// END HEADER

        // // In danh sách mặt hàng với xử lý wrapping
        // for (final item in receiptItems) {
        //   final plainItemName = removeDiacritics(item.name);
        //   const int maxWidth = 24;

        //   if (plainItemName.length > maxWidth) {
        //     final List<String> lines = wrapText(plainItemName, maxWidth);

        //     // In các dòng đầu của tên sản phẩm (nếu có nhiều dòng)
        //     for (int i = 0; i < lines.length - 1; i++) {
        //       printer.row([
        //         PosColumn(
        //           text: lines[i],
        //           width: 8, // Sử dụng toàn bộ độ rộng cho dòng tên sản phẩm
        //           styles: const PosStyles(align: PosAlign.left),
        //         ),
        //         PosColumn(
        //           text: '',
        //           width: 4,
        //           styles: const PosStyles(align: PosAlign.right)
        //         ),
        //       ]);
        //     }

        //     // In dòng cuối cùng của tên sản phẩm cùng với số lượng và giá
        //     printer.row([
        //       PosColumn(
        //         text: lines.last,
        //         width: 8,
        //         styles: const PosStyles(align: PosAlign.left)
        //       ),
        //       PosColumn(
        //         text: '${item.quantity}',
        //         width: 2,
        //         styles: const PosStyles(align: PosAlign.center)
        //       ),
        //       PosColumn(
        //         text: '${item.price}\$',
        //         width: 2,
        //         styles: const PosStyles(align: PosAlign.right)
        //       ),
        //     ]);
        //   } else {
        //     // In một dòng bình thường nếu tên ngắn
        //     printer.row([
        //       PosColumn(
        //         text: plainItemName,
        //         width: 8,
        //         styles: const PosStyles(align: PosAlign.left)
        //       ),
        //       PosColumn(
        //         text: '${item.quantity}',
        //         width: 2,
        //         styles: const PosStyles(align: PosAlign.center)
        //       ),
        //       PosColumn(
        //         text: '${item.price}\$',
        //         width: 2,
        //         styles: const PosStyles(align: PosAlign.right)
        //       ),
        //     ]);
        //   }
        // }

        // In danh sách items
        for (final item in receiptItems) {
          final nameLines = wrapItemName(removeDiacritics(item.name), 20);
          final priceLines = formatPrice(item.price, 8);
          final totalLines = max(nameLines.length, priceLines.length);

          // In dòng đầu tiên với tất cả thông tin
          printer.row([
            PosColumn(
              text: nameLines.first,
              width: 8,
              styles: const PosStyles(
                align: PosAlign.left,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: '${item.quantity}',
              width: 1,
              styles: const PosStyles(
                align: PosAlign.center,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
            PosColumn(
              text: priceLines.first,
              width: 3,
              styles: const PosStyles(
                align: PosAlign.right,
                width: PosTextSize.size1,
                height: PosTextSize.size1,
              ),
            ),
          ]);

          // In các dòng còn lại
          for (int i = 1; i < totalLines; i++) {
            printer.row([
              PosColumn(
                text: i < nameLines.length ? nameLines[i] : '',
                width: 8,
                styles: const PosStyles(
                  align: PosAlign.left,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              ),
              PosColumn(
                text: '',
                width: 1,
                styles: const PosStyles(
                  align: PosAlign.center,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              ),
              PosColumn(
                text: i < priceLines.length ? priceLines[i] : '',
                width: 3,
                styles: const PosStyles(
                  align: PosAlign.right,
                  width: PosTextSize.size1,
                  height: PosTextSize.size1,
                ),
              ),
            ]);
          }
        }

        // for (final item in receiptItems) {
        //   final plainItemName = removeDiacritics(item.name);
        //   final priceString = '${item.price}\$';
        //   const int maxNameWidth = 24;
        //   const int maxPriceWidth = 10;

        //   final List<String> nameLines = wrapText(plainItemName, maxNameWidth);
        //   final List<String> priceLines = wrapText(priceString, maxPriceWidth);

        //   final int totalLines = max(nameLines.length, priceLines.length);

        //   for (int i = 0; i < totalLines; i++) {
        //     if (i == 0) {
        //       // Dòng đầu tiên với số lượng
        //       printer.row([
        //         PosColumn(
        //           text: i < nameLines.length ? nameLines[i] : '',
        //           width: 8,
        //           styles: const PosStyles(align: PosAlign.left)
        //         ),
        //         PosColumn(
        //           text: '${item.quantity}',
        //           width: 2,
        //           styles: const PosStyles(align: PosAlign.center)
        //         ),
        //         PosColumn(
        //           text: i < priceLines.length ? priceLines[i] : '',
        //           width: 2,
        //           styles: const PosStyles(align: PosAlign.right)
        //         ),
        //       ]);
        //     } else {
        //       // Các dòng tiếp theo không có số lượng
        //       printer.row([
        //         PosColumn(
        //           text: i < nameLines.length ? nameLines[i] : '',
        //           width: 8,
        //           styles: const PosStyles(align: PosAlign.left)
        //         ),
        //         PosColumn(
        //           text: '',
        //           width: 2,
        //           styles: const PosStyles(align: PosAlign.center)
        //         ),
        //         PosColumn(
        //           text: i < priceLines.length ? priceLines[i] : '',
        //           width: 2,
        //           styles: const PosStyles(align: PosAlign.right)
        //         ),
        //       ]);
        //     }
        //   }
        // }

        // // Footer với canh lề phải cho các số tiền
        final total = receiptItems.fold(0.0, (sum, item) => sum + (item.price * (item.quantity ?? 1)));

        printer
          ..hr(ch: '=')
          ..row([
            PosColumn(text: 'TONG CONG:', width: 5, styles: const PosStyles(bold: true, align: PosAlign.left)),
            PosColumn(text: '', width: 2),
            PosColumn(text: '$total\$', width: 5, styles: const PosStyles(bold: true, align: PosAlign.right)),
          ])
          ..row([
            PosColumn(text: 'TIEN MAT:', width: 5, styles: const PosStyles(align: PosAlign.left)),
            PosColumn(text: '', width: 2),
            PosColumn(text: '$cash\$', width: 5, styles: const PosStyles(align: PosAlign.right)),
          ])
          ..row([
            PosColumn(text: 'TIEN THOI:', width: 5, styles: const PosStyles(align: PosAlign.left)),
            PosColumn(text: '', width: 2),
            PosColumn(text: '${cash - total}\$', width: 5, styles: const PosStyles(align: PosAlign.right)),
          ])
          ..hr(ch: '=');

        // QR Code
        final qrImage = await QrPainter(
          data: 'hihi haha',
          version: QrVersions.auto,
        ).toImageData(100);

        final Uint8List qrBytes = qrImage!.buffer.asUint8List();
        final img.Image? qrDecoded = img.decodeImage(qrBytes);

        if (qrDecoded != null) {
          printer.image(qrDecoded);
        }

        printer
          ..rawBytes([0x1B, 0x61, 0x01]) // Command để căn giữa
          ..text(removeDiacritics('Cam on quy khach!'), styles: const PosStyles(align: PosAlign.center, bold: true))
          // ..feed(1)
          ..cut();
      } else {
        print('Khong the ket noi may in');
      }
    } catch (e) {
      print('Loi in hoa don: $e');
    }
  }

// Future<void> printReceiptToNetworkPrinter({
//     required String ipAddress,
//     required List<ReceiptItem> receiptItems,
//     required double cash,
//     required String logoPath,
//   }) async {
//     try {
//       final profile = await CapabilityProfile.load();
//       final printer = NetworkPrinter(PaperSize.mm80, profile);

//       final connectResult = await printer.connect(ipAddress, port: 9100);
//       if (connectResult.value == 1) {
//         final image = await _generateReceiptImage(receiptItems, cash, logoPath);
//         final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
//         final uint8list = byteData!.buffer.asUint8List();
//         final decodedImage = img.decodeImage(uint8list);

//         if (decodedImage != null) {
//           printer..image(decodedImage)
//           ..feed(3)
//           ..cut()
//           ..disconnect();
//         } else {
//           print('Không thể chuyển đổi hình ảnh');
//         }
//       } else {
//         print('Không thể kết nối máy in');
//       }
//     } catch (e) {
//       print('Lỗi in hóa đơn: $e');
//     }
//   }

//   Future<ui.Image> _generateReceiptImage(
//     List<ReceiptItem> receiptItems,
//     double cash,
//     String logoPath,
//   ) async {
//     const  width = 576.0;
//     double height = 200.0; // Chiều cao khởi tạo
//     // final recorder = ui.PictureRecorder();
//     // final canvas = Canvas(recorder);
//     final paint = Paint();

//     height += receiptItems.length * 50.0; // Mỗi mục thêm 50px
//     height += 200; // Thêm không gian cho phần footer

//     final recorder = ui.PictureRecorder();
//     final canvas = Canvas(recorder);

//     // Chuẩn bị các style văn bản
//     const  headerStyle = TextStyle(
//       fontSize: 30,
//       fontWeight: FontWeight.bold,
//       color: Colors.black,
//     );

//     const  normalStyle = TextStyle(
//       fontSize: 25,
//       color: Colors.black,
//     );

//     final rightAlignStyle = normalStyle.copyWith(
//       // textAlign: TextAlign.right,
//     );

//     // Vẽ logo

//     final ByteData byteData = await rootBundle.load(logoPath);
//     final Uint8List uint8List = byteData.buffer.asUint8List();
//     // final logo = await decodeImageFromList(uint8List);
//     // // final logo = await decodeImageFromList(File(logoPath).readAsBytesSync());
//     // canvas.drawImage(logo, const Offset(10, 20), paint);
//     final logo = await decodeImageFromList(uint8List);
//     final logoWidth = 200.0; // Chiều rộng mong muốn
//     final logoHeight = 200.0; // Chiều cao mong muốn

//     // Tạo một paint để scale logo
//     final logoPaint = Paint()
//       ..filterQuality = FilterQuality.high;

//     canvas.drawImageRect(
//       logo,
//       Rect.fromLTWH(0, 0, logo.width.toDouble(), logo.height.toDouble()), // Source
//       Rect.fromLTWH(10, 20, logoWidth, logoHeight), // Destination
//       logoPaint
//     );

//     // Vẽ tiêu đề
//     _drawText(
//       canvas,
//       'SIÊU THỊ ABC',
//       const Offset(170, 40),
//       headerStyle,
//     );

//     // Vẽ đường gạch chân
//     _drawLine(canvas, const Offset(0, 80), const Offset(width, 80));

//     // Vẽ "PHIẾU TÍNH TIỀN"
//     _drawText(
//       canvas,
//       'PHIẾU TÍNH TIỀN',
//       const Offset(0, 90),
//       headerStyle,
//     );

//     double yOffset = 20;

//     // Vẽ ngày bán và nhân viên
//     _drawText(
//       canvas,
//       'Ngày bán:',
//       Offset(0, yOffset),
//       normalStyle,
//     );
//     _drawText(
//       canvas,
//       '18/12/2024',
//       Offset(width - 100, yOffset),
//       rightAlignStyle,
//     );
//     yOffset += 30;

//     _drawText(
//       canvas,
//       'Nhân viên:',
//       Offset(0, yOffset),
//       normalStyle,
//     );
//     _drawText(
//       canvas,
//       'Nguyễn Văn Tồ',
//       Offset(width - 100, yOffset),
//       rightAlignStyle,
//     );
//     yOffset += 30;

//     // Vẽ hàng mặt hàng
//     _drawLine(canvas, Offset(0, yOffset), Offset(width, yOffset));
//     yOffset += 10;

//     _drawText(
//       canvas,
//       'Mặt hàng',
//       Offset(0, yOffset),
//       headerStyle,
//     );
//     _drawText(
//       canvas,
//       'Đơn giá',
//       Offset(width * 0.5, yOffset),
//       headerStyle,
//       textAlign: TextAlign.right,
//     );
//     _drawText(
//       canvas,
//       'Số lượng',
//       Offset(width * 0.7, yOffset),
//       headerStyle,
//       textAlign: TextAlign.right,
//     );
//     _drawText(
//       canvas,
//       'Thành tiền',
//       Offset(width - 10, yOffset),
//       headerStyle,
//       textAlign: TextAlign.right,
//     );
//     yOffset += 30;

//     double total = 0;
//     for (final item in receiptItems) {
//       final itemTotal = item.price * (item.quantity ?? 1);
//       total += itemTotal;

//       _drawText(
//         canvas,
//         item.name,
//         Offset(0, yOffset),
//         normalStyle,
//       );
//       _drawText(
//         canvas,
//         '${item.price.toStringAsFixed(0)}đ',
//         Offset(width * 0.5, yOffset),
//         rightAlignStyle,
//       );
//       _drawText(
//         canvas,
//         '${item.quantity}',
//         Offset(width * 0.7, yOffset),
//         rightAlignStyle,
//       );
//       _drawText(
//         canvas,
//         '${itemTotal.toStringAsFixed(0)}đ',
//         Offset(width - 10, yOffset),
//         rightAlignStyle,
//       );
//       yOffset += 30;
//       height += 30; // Tăng chiều cao canvas
//     }

//     // Vẽ đường gạch chân
//     _drawLine(canvas, Offset(0, yOffset), Offset(width, yOffset));
//     yOffset += 10;

//     // Vẽ tổng tiền, tiền mặt và tiền thối
//     _drawText(
//       canvas,
//       'TỔNG CỘNG:',
//       Offset(0, yOffset),
//       headerStyle,
//     );
//     _drawText(
//       canvas,
//       '${total.toStringAsFixed(0)}đ',
//       Offset(width - 100, yOffset),
//       rightAlignStyle,
//     );
//     yOffset += 30;

//     _drawText(
//       canvas,
//       'Tiền mặt:',
//       Offset(0, yOffset),
//       normalStyle,
//     );
//     _drawText(
//       canvas,
//       '${cash.toStringAsFixed(0)}đ',
//       Offset(width - 100, yOffset),
//       rightAlignStyle,
//     );
//     yOffset += 30;

//     _drawText(
//       canvas,
//       'Tiền thối:',
//       Offset(0, yOffset),
//       normalStyle,
//     );
//     _drawText(
//       canvas,
//       '${(cash - total).toStringAsFixed(0)}đ',
//       Offset(width - 100, yOffset),
//       rightAlignStyle,
//     );
//     yOffset += 30;

//     // Vẽ đường gạch chân
//     _drawLine(canvas, Offset(0, yOffset), Offset(width, yOffset));
//     yOffset += 10;

//     // Vẽ mã QR
//     // final qrWidget = QrImage(
//     //   data: 'https://example.com',
//     //   version: QrVersions.auto,
//     //   size: 100,
//     // );
//     // canvas.drawWidget(qrWidget, Offset(width / 2 - 50, yOffset));
//     // yOffset += 120;

//     final qrPainter = QrPainter(
//       data: 'https://example.com', // Đường dẫn hoặc thông tin muốn mã hóa
//       version: QrVersions.auto,
//       gapless: false,
//       color: Colors.black,
//       emptyColor: Colors.white,
//     );

//     // Vẽ QR code
//     final qrSize = 50.0;
//     final qrImage = await qrPainter.toImage(qrSize);
//     canvas.drawImage(
//       qrImage,
//       Offset(width / 2 - qrSize / 2, yOffset),
//       paint
//     );
//     yOffset += qrSize + 20;

//     // Vẽ "Cảm ơn quý khách!"
//     _drawText(
//       canvas,
//       'Cảm ơn quý khách!',
//       Offset(width / 2, yOffset),
//       normalStyle,
//       textAlign: TextAlign.center,
//     );

//     final picture = recorder.endRecording();
//     return picture.toImage(width.toInt(), height.toInt());
//   }

//   double _drawText(
//     Canvas canvas,
//     String text,
//     Offset offset,
//     TextStyle style, {
//     TextAlign textAlign = TextAlign.left,
//     double maxWidth = 576,
//   }) {
//     // Tạo TextPainter với chế độ ngắt dòng
//     final textPainter = TextPainter(
//       text: TextSpan(text: text, style: style),
//       textAlign: textAlign,
//       textDirection: TextDirection.ltr,
//       maxLines: null, // Cho phép nhiều dòng
//       // ellipsis: '...', // Thêm dấu ... nếu vượt quá
//     )..layout(maxWidth: maxWidth);

//     textPainter.paint(canvas, offset);

//     // Trả về chiều cao thực tế để điều chỉnh layout
//     return textPainter.height;
//   }

//   void _drawLine(
//     Canvas canvas,
//     Offset start,
//     Offset end,
//   ) {
//     final paint = Paint()
//       ..color = Colors.black
//       ..strokeWidth = 2;
//     canvas.drawLine(start, end, paint);
//   }

  /// CALL FUNC PRINT
  // void exampleUsage(BuildContext context) async {
  //   final ReceiptPrinter receiptPrinter = ReceiptPrinter();
  //   /// In bằng hình ảnh
  //   // // Tạo widget hoá đơn -> capture -> print receipt
  //   // final Widget receiptWidget = receiptPrinter.buildReceiptWidget(
  //   //   customerName: 'Nguyễn Văn A',
  //   //   items: [
  //   //     ReceiptItem(name: 'Chả cá thác lác um meng nhiều chả ít meng', price: 165000.00),
  //   //     ReceiptItem(name: 'Đậu khuôn chiên', price: 40000.00),
  //   //     ReceiptItem(name: 'Bún đậu mắm tôm', price: 60.000),
  //   //     ReceiptItem(name: 'Mực 1 nắng, nắng thí moẹ', price: 900.000),
  //   //     ReceiptItem(name: 'Bún chả Hà Nội', price: 200.000),
  //   //     ReceiptItem(name: 'Chả cá thác lác um meng', price: 165.000),
  //   //   ],
  //   //   qrData: 'hihihaha test printer receipt',
  //   //   logoPath: 'assets/icon/icon.png',
  //   // );

  //   // // Hiển thị widget (tuỳ chọn)
  //   // // ignore: inference_failure_on_function_invocation
  //   // await showDialog(
  //   //   context: context,
  //   //   builder: (_) => Dialog(
  //   //     child: receiptWidget)
  //   // );

  //   /// IN trực tiếp với text
  //   final receiptItems1 = [
  //     ReceiptItem(name: 'Nước ngọt', price: 12345678910, quantity: 3),
  //     ReceiptItem(name: 'Bánh mì nướng muối ớt cho thật nhiều ớt đủ các thể loại, ớt bột ớt xanh ớt đổ từa lưa', price: 15000, quantity: 1),
  //     // ReceiptItem(name: 'Khăn giấy', price: 3000, quantity: 2),
  //   ];
  //    await receiptPrinter.printReceiptAsText(
  //     ipAddress: '***********',
  //     receiptItems: receiptItems1, cash: 50000.0,
  //     cashierName: 'tèo téo teo tèn ten',
  //     logoPath: 'assets/icon/icon.png',
  //   );
  // }
}

// Lớp để định nghĩa sản phẩm
class ReceiptItem {
  final String name;
  final double price;
  final double? quantity;
  final String? description;

  ReceiptItem({required this.name, required this.price, this.quantity, this.description});
}

class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double dashWidth = 9, dashSpace = 5, startX = 0;
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 1;
    while (startX < size.width) {
      canvas.drawLine(Offset(startX, 0), Offset(startX + dashWidth, 0), paint);
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
