// ignore_for_file: strict_raw_type, inference_failure_on_function_invocation, empty_catches, curly_braces_in_flow_control_structures, inference_failure_on_instance_creation

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:stickyqrbusiness/@const/colors.dart';
import 'package:stickyqrbusiness/@core/core.dart';
import 'package:stickyqrbusiness/@core/store/customer-segment/customer-segment.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-change-tab/order-active-change-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-active-time-pickup-tab/order-active-time-pickup-tab.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-prep-time/order-change-prep-time.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-change-status/order-change-status.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail-auto-reload/order-detail-auto-reload.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-detail/order-detail.cubit.dart';
import 'package:stickyqrbusiness/@core/store/order-new-notification/order-new-notification.cubit.dart';
import 'package:stickyqrbusiness/@core/store/printers-local/printers-local.cubit.dart';
import 'package:stickyqrbusiness/@utils/logger.dart';
import 'package:stickyqrbusiness/l10n/l10n.dart';
import 'package:stickyqrbusiness/pages/orders-active/widget/@orders-active-widget.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/customer-receipt-template.dart';
import 'package:stickyqrbusiness/pages/settings-orders/@widgets/customize-receipt-widget/@widgets/kitchen-ticket-template.dart';

class OrderActiveDetailPage extends StatefulWidget {
  final String orderId;
  const OrderActiveDetailPage({super.key, required this.orderId});

  @override
  State<OrderActiveDetailPage> createState() => _OrderActiveDetailPageState();
}

class _OrderActiveDetailPageState extends State<OrderActiveDetailPage> with WidgetsBindingObserver {
  OrderActiveChangeTabCubit? orderActiveChangeTabCubit;
  late final l10n = context.l10n;

  @override
  void initState() {
    AppLog.e('widget.orderId: ${widget.orderId}');
    context.read<OrderNewNotificationCubit>().onClosePage();
    orderActiveChangeTabCubit = context.read<OrderActiveChangeTabCubit>();
    orderActiveChangeTabCubit?.onChangedOrderDetailClose(OrderActiveOpen.Open);
    context.read<OrderDetailCubit>()
      ..onChangeOrderID(widget.orderId)
      ..getOrderDetail(id: widget.orderId);
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) => {});
    super.initState();
  }

  @override
  void dispose() {
    try {
      if (orderActiveChangeTabCubit != null) {
        orderActiveChangeTabCubit?.onChangedOrderDetailClose(OrderActiveOpen.Close);
      }
    } catch (e) {}

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appBlackColor,
      body: SafeArea(
        top: false,
        bottom: false,
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => OrderChangeStatusCubit(),
            ),
            BlocProvider(
              create: (context) => OrderChangePrepTimeCubit(),
            ),
            BlocProvider(
              create: (context) => OrderActiveTimePickupTabCubit(),
            ),
            BlocProvider(
              create: (context) => CustomerSegmentCubit(),
            ),
          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<OrderDetailAutoReloadCubit,
                  OrderDetailAutoReloadState>(
                listener: (context, state) {
                  switch (state.status) {
                    case OrderDetailAutoReloadStatus.Loading:
                      OrderDetailCubitRegistry.refreshOrder(widget.orderId);
                      context.read<OrderDetailAutoReloadCubit>().onChangedStatus();
                      break;
                    default:
                  }
                },
              ),
              BlocListener<OrderChangeStatusCubit, OrderChangeStatusState>(
                listener: (context, state) async {
                  if (state.status == OrderChangeStatusStatus.Success) {
                    /// field for print
                    final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
                    final KitchenTicketTemplateWidget kitchenPrint = KitchenTicketTemplateWidget();
                    final CustomerReceiptTemplateWidget customerPrint = CustomerReceiptTemplateWidget();
                    final printersReceipt = context.read<PrintersLocalCubit>().state.printersReceiptEnable ?? [];
                    final printersKitchenTicket = context.read<PrintersLocalCubit>().state.printersKitchenTicketEnable ?? [];
                    final order = state.order;
                    if (order?.status == OrderStatusDefine.PREPARING.name) {
                      // final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
                      // final KitchenTicketTemplateWidget kitchenPrint = KitchenTicketTemplateWidget();
                      // final CustomerReceiptTemplateWidget customerPrint = CustomerReceiptTemplateWidget();

                      // final printersReceipt = context.read<PrintersLocalCubit>().state.printersReceiptEnable ?? [];
                      // final printersKitchenTicket = context.read<PrintersLocalCubit>().state.printersKitchenTicketEnable ?? [];
                      // AppLog.e('order = ${jsonEncode(printersReceipt)}');
                      AppLog.e('printersReceipt = ${printersReceipt.length}');
                      AppLog.e('printersKitchenTicket = ${printersKitchenTicket.length}');
                      AppLog.d('order = ${jsonEncode(order)}');
                      AppLog.e('PRINT in ORDER ACTIVE DETAIL PAGE');
                      // // in kitchen receipt
                      // await onPrintKitchenTicket(
                      //   l10n: l10n,
                      //   order: order ?? Order(),
                      //   timeZone: timeZone,
                      //   printersKitchen: printersKitchenTicket,
                      //   kitchenPrint: kitchenPrint,
                      // );
                      // await Future.delayed(const Duration(milliseconds: 500));
                      // // in customer receipt
                      // await onPrintCustomerReceipt(
                      //   l10n: l10n,
                      //   order: order ?? Order(),
                      //   printersReceipt: printersReceipt,
                      //   customerPrint: customerPrint,
                      // );
                      // await PrintReceiptService().printCustomerReceipt(
                      //   context: context,
                      //   l10n: l10n,
                      //   order: order ?? Order(),
                      //   printersReceipt: printersReceipt,
                      //   customerPrint: customerPrint,
                      // );
                      // await PrintReceiptService().printAll(
                      //   context: context,
                      //   l10n: l10n,
                      //   order: order ?? Order(),
                      //   timeZone: timeZone,
                      //   printersReceipt: printersReceipt,
                      //   printersKitchen: printersKitchenTicket,
                      //   customerPrint: customerPrint,
                      //   kitchenPrint: kitchenPrint,
                      // );

                      // if (printersReceipt.isEmpty) {
                      //   AppBased.toastError(context, title: l10n.printersAvailable);
                      //   return;
                      // } else {
                      //   for (final printer in printersReceipt) {
                      //     final ip = printer.address ?? '';
                      //     if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
                      //     await customerPrint.printCustomerReceiptAsText(
                      //       context: context,
                      //       ipAddress: printer.address ?? '',
                      //       order: _order ?? Order(),
                      //     );
                      //   }
                      // }
                      // if (printersKitchenTicket.isEmpty) {
                      //   AppBased.toastError(context, title: l10n.printersAvailable);
                      //   return;
                      // } else {
                      //   // if (printersKitchenTicket.length > 1) {
                      //     for (final printer in printersKitchenTicket) {
                      //       final ip = printer.address ?? '';
                      //       if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
                      //       await kitchenPrint.printKitchenTicketAsText(
                      //         // context: context,
                      //         l10n: l10n,
                      //         timezone: timeZone,
                      //         ipAddress: printer.address ?? '',
                      //         order: _order ?? Order(),
                      //       );
                      //     }
                      //     // showPrintReceiptDialog(context, _order ?? Order(), printersKitchenTicket);
                      //   // }
                      // }
                    }
                  }
                },
              ),
            ],
            child: BlocConsumer<OrderDetailCubit, OrderDetailState>(
              listener: (context, state) {
                switch (state.status) {
                  case OrderDetailStatus.Error:
                    if (state.errorMsg != null && state.errorMsg != '') {
                      AppBased.toastError(context, title: state.errorMsg);
                      BlocProvider.of<OrderDetailCubit>(context).onResetStatus();
                    }
                    return;
                  case OrderDetailStatus.Success:
                    final order = state.order;
                    if (order != null) {
                      context.read<CustomerSegmentCubit>().getCustomersSegment([order.customerId ?? '']);

                      if (order.status != null && order.status?.toUpperCase() == OrderStatusDefine.COMPLETED.name && order.isChangeStatusLocal != true) {
                        try {
                          Future.delayed(const Duration(milliseconds: 500), () {
                            Navigator.pop(context);
                          });
                        } catch (e) {}
                      }
                    }
                    return;
                  default:
                }
              },
              builder: (context, state) {
                switch (state.status) {
                  case OrderDetailStatus.Loading:
                    return const OrderDetailTabletLoading();
                  case OrderDetailStatus.Error:
                    return _buildEmpty(context, state.oID ?? '');
                  case OrderDetailStatus.Success:
                    final order = state.order;
                    if (order != null) {
                      return OrderActiveDetailContent(order: order);
                    } else {
                      return _buildEmpty(context, state.oID ?? '');
                    }
                  default:
                    return _buildEmpty(context, state.oID ?? '');
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmpty(BuildContext context, String id) {
    final l10n = context.l10n;
    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0.0,
        backgroundColor: AppColors.lightPrimaryBackgroundColor,
        elevation: 0,
        leading: Padding(
          padding: const EdgeInsets.all(10.0),
          child: SizedBox(
            width: 40,
            child: MaterialButton(
              elevation: 0,
              highlightElevation: 0,
              hoverElevation: 0,
              hoverColor: AppColors.lightPrimaryBackgroundColor,
              onPressed: () => {
                Navigator.of(context).pop(),
              },
              color: AppColors.lightPrimaryBackgroundColor,
              padding: EdgeInsets.zero,
              shape: const CircleBorder(),
              child: SvgPicture.asset(
                'assets/svgs/close.svg',
                colorFilter: const ColorFilter.mode(
                  AppColors.darkPrimaryBackgroundColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        color: AppColors.appColor,
        onRefresh: () => OrderDetailCubitRegistry.refreshOrder(id),
        child: ColoredBox(
          color: AppColors.lightPrimaryBackgroundColor,
          child: CustomScrollView(
            primary: false,
            physics: const AlwaysScrollableScrollPhysics(
              parent: ClampingScrollPhysics(),
            ),
            slivers: [
              SliverAppBar(
                toolbarHeight: 0,
                elevation: 0,
                automaticallyImplyLeading: false,
                backgroundColor: AppColors.anouncementBGColor,
                shadowColor: AppColors.anouncementBGColor,
                flexibleSpace: _buildAppBar(context),
                pinned: true,
                floating: false,
                snap: false,
              ),
              SliverList(
                delegate: SliverChildListDelegate([
                  SizedBox(
                    height: MediaQuery.of(context).size.height / 1.5,
                    child: Center(
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            OrderDetailEmptyWidget(
                              errMsg: l10n.unableToConnect,
                              iconName: 'order-item-empty',
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      elevation: 0,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      leading: Padding(
        padding: const EdgeInsets.all(12.0),
        child: SizedBox(
          width: 40,
          child: MaterialButton(
            elevation: 0,
            highlightElevation: 0,
            hoverElevation: 0,
            hoverColor: AppColors.anouncementBGColor,
            onPressed: () => {
              Navigator.of(context).pop(),
            },
            color: AppColors.lightPrimaryBackgroundColor,
            padding: EdgeInsets.zero,
            shape: const CircleBorder(),
            child: SvgPicture.asset(
              'assets/svgs/close.svg',
              colorFilter: const ColorFilter.mode(
                AppColors.darkPrimaryBackgroundColor,
                BlendMode.srcIn,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> onPrintCustomerReceipt({
    required AppLocalizations l10n,
    required Order order,
    required List<Printer> printersReceipt,
    required CustomerReceiptTemplateWidget customerPrint,
  }) async {
    // final customerPrint = CustomerReceiptTemplateWidget();
    // final printersReceipt = context.read<PrintersLocalCubit>().state.printersReceiptEnable ?? [];

    if (printersReceipt.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    } else {
      for (final printer in printersReceipt) {
        final ip = printer.address ?? '';
        
        // Kiểm tra điều kiện cơ bản
        if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
        
        // Lấy số lượng bản sao cần in
        final receiptCopies = printer.receiptCopies ?? 1;
        
        // Đảm bảo số lượng bản sao hợp lệ (ít nhất là 1)
        final numberOfCopies = receiptCopies > 0 ? receiptCopies : 1;
        
        AppLog.d('Printing $numberOfCopies copies for printer ${printer.address}');
        
        // In theo số lượng bản sao đã định
        for (int copyIndex = 1; copyIndex <= numberOfCopies; copyIndex++) {
          try {
            await customerPrint.printCustomerReceiptAsText(
              context: context,
              ipAddress: printer.address ?? '',
              order: order,
            );
            
            AppLog.d('Printed copy $copyIndex/$numberOfCopies for printer ${printer.address}');
            
            // Tùy chọn: thêm delay nhỏ giữa các lần in để tránh quá tải máy in
            if (copyIndex < numberOfCopies) {
              await Future.delayed(const Duration(milliseconds: 500));
            }
            
          } catch (e) {
            AppLog.e('Error printing copy $copyIndex for printer ${printer.address}: $e');
            // Có thể break hoặc continue tùy theo yêu cầu
            break; // Dừng in các bản sao còn lại nếu có lỗi
            // continue; // Tiếp tục in bản sao tiếp theo dù có lỗi
          }
        }
      }
    }
  }

  Future<void> onPrintKitchenTicket({
    required AppLocalizations l10n,
    required Order order,
    required String timeZone,
    required List<Printer> printersKitchen,
    required KitchenTicketTemplateWidget kitchenPrint,
  }) async {
    // final String timeZone = context.read<AuthBloc>().state.businessTimeZone.toString();
    // final kitchenPrint = KitchenTicketTemplateWidget();
    // final printersKitchen = context.read<PrintersLocalCubit>().state.printersKitchenTicketEnable ?? [];

    if (printersKitchen.isEmpty) {
      AppBased.toastError(context, title: l10n.printersAvailable);
      return;
    } else {
      for (final printer in printersKitchen) {
        final ip = printer.address ?? '';
        AppLog.e('ip printer = $ip');
        
        // Kiểm tra điều kiện cơ bản
        if (ip.isEmpty || printer.isAutoPrintOrderInProgress == false) continue;
        
        // Kiểm tra kitchenCategories
        final kitchenCategories = printer.kitchenCategories ?? <String>[];
        
        // Nếu kitchenCategories rỗng thì không gửi lệnh in
        if (kitchenCategories.isEmpty) {
          AppLog.d('Printer ${printer.address} has empty kitchenCategories, skipping...');
          continue;
        }
        
        // Tạo order mới chỉ chứa các item được phép in
        final filteredOrder = _createFilteredOrder(order, kitchenCategories);
        
        // Nếu không có item nào được phép in thì bỏ qua máy in này
        if (filteredOrder == null || _isOrderEmpty(filteredOrder)) {
          AppLog.d('No items to print for printer ${printer.address}');
          continue;
        }
        
        AppLog.e('filteredOrder == ${jsonEncode(filteredOrder)}');
        // In order đã được lọc
        await kitchenPrint.printKitchenTicketAsText(
          l10n: l10n,
          timezone: timeZone,
          ipAddress: printer.address ?? '',
          order: filteredOrder,
        );
        
        AppLog.d('Printed filtered order for printer ${printer.address}');
      }
    }
  }

  // Hàm tạo order mới chỉ chứa các item được phép in
  Order? _createFilteredOrder(Order? originalOrder, List<String> allowedCategoryIds) {
    if (originalOrder == null) return null;
    
    // Tạo bản copy của order gốc
    final filteredOrder = originalOrder.copyWith();
    
    // Lọc các item dựa trên categories được phép
    final filteredItems = <ItemOrder>[];
    
    for (final item in originalOrder.items ?? <ItemOrder>[]) {
      final productCategories = item.product?.categories ?? <ProductOrderCetegories>[];
      
      // Kiểm tra xem item có category được phép in không
      bool hasAllowedCategory = false;
      for (final category in productCategories) {
        if (allowedCategoryIds.contains(category.id)) {
          hasAllowedCategory = true;
          break;
        }
      }
      
      // Nếu item có category được phép thì thêm vào danh sách
      if (hasAllowedCategory) {
        filteredItems.add(item);
      }
    }
    
    // Cập nhật danh sách items đã lọc
    return filteredOrder.copyWith(items: filteredItems);
  }

  // Hàm kiểm tra order có rỗng không
  bool _isOrderEmpty(Order order) {
    return order.items == null || order.items!.isEmpty;
  }
  
}
